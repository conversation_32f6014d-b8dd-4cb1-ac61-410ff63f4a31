{% extends "base.html" %}

{% block content %}
<div class="admin-login-container">
    <h1>Admin <PERSON>gin</h1>
    
    {% if error %}
    <div class="error-message">
        {{ error }}
    </div>
    {% endif %}
    
    <form method="POST" action="{{ url_for('admin.admin_login') }}" class="admin-login-form">
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required>
        </div>
        
        <button type="submit" class="btn login-btn">Login</button>
    </form>
    
    <div class="back-link">
        <a href="{{ url_for('auth.login') }}">← Back to main login</a>
    </div>
</div>

<style>
    .admin-login-container {
        max-width: 500px;
        margin: 50px auto;
        padding: 30px;
        background-color: #333;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
        border: 1px solid #444;
    }
    
    .admin-login-container h1 {
        text-align: center;
        margin-bottom: 30px;
        color: #f7931a;
        font-size: 28px;
    }
    
    .error-message {
        background-color: rgba(231, 76, 60, 0.2);
        color: #e74c3c;
        padding: 12px;
        border-radius: 4px;
        margin-bottom: 20px;
        text-align: center;
        border-left: 4px solid #e74c3c;
    }
    
    .admin-login-form .form-group {
        margin-bottom: 20px;
    }
    
    .admin-login-form label {
        display: block;
        margin-bottom: 8px;
        color: #f0f0f0;
        font-weight: 500;
    }
    
    .admin-login-form input {
        width: 100%;
        padding: 12px;
        border: 1px solid #555;
        border-radius: 4px;
        background-color: #222;
        color: #f0f0f0;
        font-size: 16px;
        transition: border-color 0.3s, box-shadow 0.3s;
    }
    
    .admin-login-form input:focus {
        border-color: #f7931a;
        outline: none;
        box-shadow: 0 0 0 2px rgba(247, 147, 26, 0.3);
    }
    
    .login-btn {
        width: 100%;
        padding: 14px;
        background-color: #f7931a;
        color: white;
        font-weight: bold;
        font-size: 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s, transform 0.2s;
        margin-top: 10px;
    }
    
    .login-btn:hover {
        background-color: #e67e22;
        transform: translateY(-2px);
    }
    
    .login-btn:active {
        transform: translateY(0);
    }
    
    .back-link {
        text-align: center;
        margin-top: 25px;
    }
    
    .back-link a {
        color: #aaa;
        text-decoration: none;
        font-size: 14px;
        transition: color 0.3s;
    }
    
    .back-link a:hover {
        color: #f7931a;
    }
</style>
{% endblock %}
