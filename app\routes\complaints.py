import os
import json
from flask import Blueprint, jsonify, request, session, render_template
from app.models.models import Topic, Vote
from app.extensions import db
from app.utils.bot_manager import queue_discord_message
from datetime import datetime
import uuid
from sqlalchemy import or_

def get_user_vote(topic_id, user_id):
    """Helper function to get user's vote status for a topic"""
    vote = Vote.query.filter_by(
        topic_id=topic_id,
        user_id=user_id
    ).first()
    return vote.vote_type if vote else None

complaints = Blueprint('complaints', __name__)

@complaints.route('/complaints')
def show_complaints():
    """Route to show the complaints submission form"""
    form_token = str(uuid.uuid4())
    session['form_token'] = form_token
    return render_template('complaints.html', title='File Complaint', form_token=form_token)

@complaints.route('/api/forum/complaints')
def get_forum_complaints():
    try:
        page = request.args.get('page', 1, type=int)
        search_query = request.args.get('query', '')
        sort_by = request.args.get('sort', 'date')
        per_page = 10
        user_id = session.get('user_id', request.remote_addr)

        # Start with approved complaints only
        query = Topic.query.filter_by(status='approved')

        if search_query:
            query = query.filter(
                or_(
                    Topic.title.ilike(f'%{search_query}%'),
                    Topic.full_message.ilike(f'%{search_query}%')
                )
            )

        # Apply sorting
        if sort_by == 'rage':
            query = query.order_by(Topic.rage_level.desc())
        else:  # default to date
            query = query.order_by(Topic.created_at.desc())

        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        complaints = pagination.items

        # Load quitting options for text mapping
        with open('app/config/form_options.json') as f:
            form_options = json.load(f)
            quitting_options = {opt['value']: opt['text'] for opt in form_options['quittingOptions']}

        return jsonify({
            'status': 'success',
            'complaints': [{
                'id': c.id,
                'title': c.title,
                'author': c.author,
                'date': c.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'full_message': c.full_message,
                'complaint_type': c.complaint_type,
                'rage_level': c.rage_level,
                'blame': c.blame,
                'quitting': quitting_options.get(c.quitting, c.quitting),
                'reimbursement': c.reimbursement,
                'zkill_link': c.zkill_link if hasattr(c, 'zkill_link') else None,
                'comments': len(c.comments) if hasattr(c, 'comments') else 0,
                'upvotes': c.upvotes or 0,
                'downvotes': c.downvotes or 0,
                'user_vote': get_user_vote(c.id, user_id)
            } for c in complaints],
            'total_pages': pagination.pages,
            'current_page': page
        })

    except Exception as e:
        print(f"Error in get_forum_complaints: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@complaints.route('/api/complaints')
def get_complaints():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 10
        sort_by = request.args.get('sort', 'date')
        
        # Query approved complaints
        query = Topic.query.filter_by(status='approved')
        
        # Apply sorting
        if sort_by == 'date':
            query = query.order_by(Topic.created_at.desc())
        
        pagination = query.paginate(
            page=page, 
            per_page=per_page,
            error_out=False
        )

        complaints = [{
            'id': c.id,
            'title': c.title,
            'author': c.author,
            'date': c.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'preview': c.preview or (c.full_message[:100] + '...' if len(c.full_message) > 100 else c.full_message),
            'complaint_type': c.complaint_type,
            'rage_level': c.rage_level,
            'zkill_link': c.zkill_link if hasattr(c, 'zkill_link') else None,
            'status': c.status
        } for c in pagination.items]

        return jsonify({
            'status': 'success',
            'complaints': complaints,
            'current_page': page,
            'total_pages': pagination.pages,
            'total_complaints': pagination.total
        })

    except Exception as e:
        print(f"Error in get_complaints: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@complaints.route('/api/admin/complaints/<int:id>')
def get_complaint(id):
    try:
        complaint = Topic.query.get_or_404(id)
        return jsonify({
            'id': complaint.id,
            'title': complaint.title,
            'author': complaint.author,
            'date': complaint.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'status': complaint.status,
            'full_message': complaint.full_message,
            'preview': complaint.preview,
            'complaint_type': complaint.complaint_type,
            'rage_level': complaint.rage_level,
            'zkill_link': complaint.zkill_link if hasattr(complaint, 'zkill_link') else None
        })
    except Exception as e:
        print(f"Error in get_complaint: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@complaints.route('/complaint/<int:complaint_id>')
def view_complaint(complaint_id):
    complaint = Topic.query.get_or_404(complaint_id)
    return render_template('view_complaint.html', 
                         title=complaint.title or 'Untitled Complaint',
                         complaint=complaint)

@complaints.route('/api/complaints/<int:id>')
def get_complaint_details(id):
    try:
        complaint = Topic.query.get_or_404(id)
        
        return jsonify({
            'status': 'success',
            'id': complaint.id,
            'title': complaint.title,
            'author': complaint.author,
            'date': complaint.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'full_message': complaint.full_message,
            'complaint_type': complaint.complaint_type,
            'blame': complaint.blame,
            'rage_level': complaint.rage_level,
            'quitting': complaint.quitting,
            'zkill_link': complaint.zkill_link if hasattr(complaint, 'zkill_link') else None
        })
    except Exception as e:
        print(f"Error in get_complaint_details: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@complaints.route('/api/forum/complaints/<int:complaint_id>', methods=['GET'])
def get_forum_complaint(complaint_id):
    try:
        complaint = Topic.query.get_or_404(complaint_id)
        user_id = session.get('user_id', request.remote_addr)
        
        return jsonify({
            'status': 'success',
            'id': complaint.id,
            'title': complaint.title,
            'author': complaint.author,
            'date': complaint.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'full_message': complaint.full_message,
            'preview': complaint.preview,
            'complaint_type': complaint.complaint_type,
            'rage_level': complaint.rage_level,
            'zkill_link': complaint.zkill_link if hasattr(complaint, 'zkill_link') else None,
            'user_vote': get_user_vote(complaint.id, user_id)
        })
    except Exception as e:
        print(f"Error in get_forum_complaint: {str(e)}")  # Add this line for debugging
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@complaints.route('/post_to_discord', methods=['POST'])
def post_complaint():
    try:
        # Debug print to check what's being received
        print(f"Form token: {request.form.get('form_token')}")
        print(f"Session token: {session.get('form_token')}")

        # Verify form token to prevent duplicate submissions
        form_token = request.form.get('form_token')
        session_token = session.get('form_token')
        
        if not form_token:
            return jsonify({
                'status': 'error',
                'message': 'Missing form token'
            })

        if not session_token:
            # Generate new token if none exists
            new_token = str(uuid.uuid4())
            session['form_token'] = new_token
            return jsonify({
                'status': 'error',
                'message': 'Session expired, please try again'
            })

        if form_token != session_token:
            return jsonify({
                'status': 'duplicate',
                'message': 'This complaint has already been submitted'
            })

        # Clear the form token from session ONLY after successful validation
        session.pop('form_token', None)

        # Create new Topic in database
        new_complaint = Topic(
            title=request.form.get('title'),
            author=request.form.get('name', 'Anonymous'),  # Changed from 'author' to 'name' to match form field
            full_message=request.form.get('message'),
            complaint_type=request.form.get('complaint_type'),
            blame=request.form.get('blame'),
            rage_level=request.form.get('rage_level'),
            quitting=request.form.get('quitting'),  # This should now store the text value
            zkill_link=request.form.get('zkill_link'),
            status='pending',
            created_at=datetime.utcnow()
        )

        # Add to database
        db.session.add(new_complaint)
        db.session.commit()

        # Create complaint data dictionary
        complaint_data = {
            'id': new_complaint.id,
            'title': request.form.get('title'),
            'author': request.form.get('author', 'Anonymous'),
            'message': request.form.get('message'),
            'complaint_type': request.form.get('complaint_type'),
            'blame': request.form.get('blame'),
            'rage_level': request.form.get('rage_level'),
            'quitting': request.form.get('quitting'),
            'zkill_link': request.form.get('zkill_link'),
            'reimbursement': request.form.get('reimbursement', 'No')
        }

        # Queue the Discord message
        if queue_discord_message(complaint_data):
            return jsonify({
                'status': 'success',
                'complaint_id': new_complaint.id
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to send complaint to Discord'
            })

    except Exception as e:
        print(f"Error in post_complaint: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@complaints.route('/api/complaints/<int:complaint_id>/vote', methods=['POST'])
def vote_complaint(complaint_id):
    try:
        complaint = Topic.query.get_or_404(complaint_id)
        user_id = session.get('user_id', request.remote_addr)
        vote_type = request.json.get('vote_type')

        if vote_type not in ['up', 'down']:
            return jsonify({'status': 'error', 'message': 'Invalid vote type'})

        existing_vote = Vote.query.filter_by(
            topic_id=complaint_id,
            user_id=user_id
        ).first()

        if existing_vote:
            if existing_vote.vote_type == vote_type:
                # Remove vote if clicking the same button again
                db.session.delete(existing_vote)
                db.session.commit()
                update_vote_counts(complaint_id)
                return jsonify({
                    'status': 'success',
                    'action': 'removed',
                    'user_vote': None
                })
            else:
                # Change vote if clicking different button
                existing_vote.vote_type = vote_type
                db.session.commit()
                update_vote_counts(complaint_id)
                return jsonify({
                    'status': 'success',
                    'action': 'changed',
                    'user_vote': vote_type
                })
        else:
            # Add new vote
            new_vote = Vote(
                topic_id=complaint_id,
                user_id=user_id,
                vote_type=vote_type
            )
            db.session.add(new_vote)
            db.session.commit()
            update_vote_counts(complaint_id)
            return jsonify({
                'status': 'success',
                'action': 'added',
                'user_vote': vote_type
            })

    except Exception as e:
        print(f"Error in vote_complaint: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)})

def update_vote_counts(topic_id):
    """Update the vote counts for a topic"""
    topic = Topic.query.get(topic_id)
    if topic:
        upvotes = Vote.query.filter_by(topic_id=topic_id, vote_type='up').count()
        downvotes = Vote.query.filter_by(topic_id=topic_id, vote_type='down').count()
        topic.upvotes = upvotes
        topic.downvotes = downvotes
        db.session.commit()

@complaints.route('/api/topics/<int:topic_id>/comments', methods=['GET', 'POST'])
def handle_comments(topic_id):
    if request.method == 'GET':
        comments = Comment.query.filter_by(topic_id=topic_id).order_by(Comment.timestamp.desc()).all()
        return jsonify([{
            'id': comment.id,
            'author': comment.author,
            'text': comment.text,
            'timestamp': comment.timestamp.strftime('%B %d, %Y %H:%M')
        } for comment in comments])
    
    elif request.method == 'POST':
        try:
            text = request.json.get('text')
            if not text:
                return jsonify({'status': 'error', 'message': 'Comment text is required'})

            new_comment = Comment(
                topic_id=topic_id,
                author=session.get('username', 'Anonymous'),
                text=text,
                timestamp=datetime.utcnow()
            )
            db.session.add(new_comment)
            db.session.commit()

            return jsonify({
                'status': 'success',
                'comment': {
                    'id': new_comment.id,
                    'author': new_comment.author,
                    'text': new_comment.text,
                    'timestamp': new_comment.timestamp.strftime('%B %d, %Y %H:%M')
                }
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'status': 'error', 'message': str(e)}), 500

@complaints.route('/api/form-options', methods=['GET'])
def get_public_form_options():
    try:
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'form_options.json')
        
        if not os.path.exists(config_path):
            return jsonify({
                'status': 'success',
                'data': {
                    'complaintTypes': [],
                    'blameOptions': [],
                    'rageLevels': [],
                    'quittingOptions': []
                }
            })
        
        with open(config_path, 'r', encoding='utf-8') as f:
            options = json.load(f)
            
        return jsonify({
            'status': 'success',
            'data': options
        })
    except Exception as e:
        print(f"Error loading form options: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500
