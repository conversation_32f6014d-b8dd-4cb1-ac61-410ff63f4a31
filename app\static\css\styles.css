.complaint-card {
    display: flex;
    background-color: #1a1a1a;
    border: 1px solid #2a2a2a;
    margin-bottom: 8px;
    padding: 8px 0;
    transition: background-color 0.2s ease;
}

.complaint-card:hover {
    background-color: #252525;
}

.complaint-votes {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 40px;
    padding: 0 4px;
}

.vote-btn {
    border: none;
    background: none;
    cursor: pointer;
    padding: 5px 10px;
    transition: color 0.2s;
}

.vote-btn.voted {
    color: #ff4500; /* Reddit-style upvote color */
}

.vote-btn.downvote.voted {
    color: #7193ff; /* Reddit-style downvote color */
}

.vote-count {
    margin: 0 8px;
    font-weight: bold;
}

.complaint-content {
    flex: 1;
    min-width: 0;
    padding-right: 8px;
}

.complaint-main {
    display: flex;
    align-items: center;
    gap: 8px;
}

.complaint-title {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
}

.complaint-title a {
    color: #d7dadc;
    text-decoration: none;
}

.complaint-title a:hover {
    color: #0079d3;
}

.complaint-meta {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #818384;
    margin-top: 4px;
}

.complaint-type {
    color: #4fbcff;
}

.complaint-author {
    color: #4fbcff;
    text-decoration: none;
}

.complaint-author:hover {
    text-decoration: underline;
}

.complaint-date {
    color: #818384;
}

.complaint-date::before {
    content: "•";
    margin: 0 4px;
}

/* Rage level fire icon colors */
.complaints-table .rage-icon,
.complaint-popup .rage-icon {
    font-size: 1.2em;
    transition: color 0.2s ease;
}

.complaints-table .rage-icon.level-1,
.complaint-popup .rage-icon.level-1 { color: #87CEEB; } /* Sky blue - coolest */

.complaints-table .rage-icon.level-2,
.complaint-popup .rage-icon.level-2 { color: #98FB98; }

.complaints-table .rage-icon.level-3,
.complaint-popup .rage-icon.level-3 { color: #90EE90; }

.complaints-table .rage-icon.level-4,
.complaint-popup .rage-icon.level-4 { color: #FFFF00; }

.complaints-table .rage-icon.level-5,
.complaint-popup .rage-icon.level-5 { color: #FFA500; }

.complaints-table .rage-icon.level-6,
.complaint-popup .rage-icon.level-6 { color: #FF8C00; }

.complaints-table .rage-icon.level-7,
.complaint-popup .rage-icon.level-7 { color: #FF6347; }

.complaints-table .rage-icon.level-8,
.complaint-popup .rage-icon.level-8 { color: #FF4500; }

.complaints-table .rage-icon.level-9,
.complaint-popup .rage-icon.level-9 { color: #FF0000; }

.complaints-table .rage-icon.level-10,
.complaint-popup .rage-icon.level-10 { 
    color: #8B0000;
    text-shadow: 0 0 10px #FF0000;
}  /* Dark red - hottest */

/* Optional: Add a glow effect for level 10 */
.rage-icon.level-10 {
    text-shadow: 0 0 10px #FF0000;
}

/* Popup styles */
.complaint-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.9);
    display: none;
    z-index: 1000;
}

.complaint-popup.active {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 60px;
}

.complaint-popup-content {
    background-color: #1a1a1a;
    border-radius: 4px;
    max-width: 800px;
    width: 95%;
    max-height: 80vh;
    overflow-y: auto;
    padding: 20px;
    position: relative;
}

.popup-close {
    position: absolute;
    top: 12px;
    right: 12px;
    background: none;
    border: none;
    color: #818384;
    cursor: pointer;
    font-size: 20px;
}

.popup-close:hover {
    color: #d7dadc;
}
