import json
from flask import Blueprint, render_template
from app.models.models import Topic
from datetime import datetime

main = Blueprint('main', __name__)

def load_form_options():
    """Load form options from JSON file"""
    try:
        with open('app/config/form_options.json', 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading form options: {e}")
        return {}

def get_option_text(options, value):
    """Get the display text for a given option value"""
    try:
        for option in options:
            if option['value'] == value:
                return option['text']
        return value  # Return original value if no match found
    except Exception as e:
        print(f"Error getting option text: {e}")
        return value

@main.route('/')
def home():
    return render_template('index.html', title='Home')

@main.route('/forums')
def forums():
    topics = Topic.query.filter_by(status='approved').order_by(Topic.created_at.desc()).all()
    return render_template('forums.html', 
                         title='Forums', 
                         topics=topics,
                         load_form_options=load_form_options,
                         get_option_text=get_option_text)
