// Function to add a new option
function addOption(containerId) {
    const container = document.getElementById(containerId);
    const newOption = document.createElement('div');
    newOption.className = 'option-item';
    newOption.innerHTML = `
        <input type="text" value="" class="option-input" placeholder="Enter new option">
        <button type="button" class="delete-option-btn" onclick="deleteOption(this)">Delete</button>
    `;
    container.appendChild(newOption);
}

// Function to delete an option
function deleteOption(button) {
    const optionItem = button.closest('.option-item');
    if (optionItem) {
        optionItem.remove();
    }
}

// Function to get options from a container
function getOptionsFromContainer(containerId) {
    const container = document.getElementById(containerId);
    const options = [];
    container.querySelectorAll('.option-input').forEach(input => {
        if (input.value.trim()) {
            options.push({
                text: input.value.trim(),
                value: input.value.trim().toLowerCase().replace(/[^a-z0-9]+/g, '_')
            });
        }
    });
    return options;
}

// Function to save all options
function saveAllOptions() {
    // Filter out empty options
    const getValidOptions = (selector) => {
        return Array.from(document.querySelectorAll(selector))
            .map(input => input.value.trim())
            .filter(value => value !== '')
            .map(value => ({
                text: value,
                value: value.toLowerCase().replace(/[^a-z0-9]+/g, '_')
            }));
    };

    const formOptions = {
        complaintTypes: getValidOptions('#complaintTypeOptions .option-input'),
        blameOptions: getValidOptions('#blameOptions .option-input'),
        rageLevels: getValidOptions('#rageLevelOptions .option-input'),
        quittingOptions: getValidOptions('#quittingOptions .option-input')
    };

    fetch('/api/admin/save-form-options', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formOptions),
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            if (response.status === 401) {
                window.location.href = '/admin/login';
                throw new Error('Session expired. Please login again.');
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.status === 'success') {
            alert('Options saved successfully');
            loadFormOptions();
        } else {
            throw new Error(data.message || 'Failed to save options');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert(error.message || 'Error saving options');
    });
}

// Function to load form options
function loadFormOptions() {
    fetch('/api/admin/form-options')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success' && data.data) {
                const options = data.data;
                
                // Clear existing options
                ['complaintTypeOptions', 'blameOptions', 'rageLevelOptions', 'quittingOptions']
                    .forEach(id => document.getElementById(id).innerHTML = '');

                // Populate options
                if (options.complaintTypes) {
                    options.complaintTypes.forEach(opt => addOptionToList('complaintTypeOptions', opt));
                }
                if (options.blameOptions) {
                    options.blameOptions.forEach(opt => addOptionToList('blameOptions', opt));
                }
                if (options.rageLevels) {
                    options.rageLevels.forEach(opt => addOptionToList('rageLevelOptions', opt));
                }
                if (options.quittingOptions) {
                    options.quittingOptions.forEach(opt => addOptionToList('quittingOptions', opt));
                }
            }
        })
        .catch(error => {
            console.error('Error loading options:', error);
            alert('Error loading options: ' + error.message);
        });
}

// Function to add an option to a list
function addOptionToList(containerId, option) {
    const container = document.getElementById(containerId);
    const newOption = document.createElement('div');
    newOption.className = 'option-item';
    newOption.innerHTML = `
        <input type="text" value="${option.text}" class="option-input">
        <button type="button" class="delete-option-btn" onclick="deleteOption(this)">Delete</button>
    `;
    container.appendChild(newOption);
}

// Initialize when the document is loaded
document.addEventListener('DOMContentLoaded', function() {
    loadFormOptions();
    
    // Add save button handler
    document.getElementById('saveFormChanges').addEventListener('click', saveAllOptions);
    
    // Add reset button handler
    document.getElementById('resetDefaults').addEventListener('click', function() {
        if (confirm('Are you sure you want to reset all options to defaults?')) {
            fetch('/api/admin/form-options/reset', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    location.reload();
                } else {
                    alert('Error resetting options: ' + data.message);
                }
            })
            .catch(error => {
                alert('Error resetting to defaults: ' + error.message);
            });
        }
    });
});
