{% extends "base.html" %}

{% block content %}
<div class="login-container">
    <div class="login-header">
        <img src="https://images.evetech.net/alliances/********/logo?size=128" alt="Sky Daddy Ranch Logo" class="ranch-logo">
        <h1>Sky Daddy Ranch Login</h1>
    </div>
    
    <div class="login-options">
        <div class="eve-login-option">
            <p>Log in with your EVE Online account to access Sky Daddy Ranch services.</p>
            <a href="https://seat.skydaddyranch.com" class="eve-login-button" target="_blank">
                <img src="{{ url_for('static', filename='images/eve-sso-login-white-large.png') }}" alt="LOG IN with EVE Online">
            </a>
            <p class="login-note">We use EVE's official SSO system to authenticate users.</p>
        </div>
        
        <div class="admin-login-link">
            <p>Site administrators can <a href="/admin/login">log in here</a>.</p>
        </div>
    </div>
</div>

<style>
    .login-container {
        max-width: 600px;
        margin: 50px auto;
        padding: 30px;
        background-color: #333;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        text-align: center;
    }
    
    .login-header {
        margin-bottom: 30px;
    }
    
    .ranch-logo {
        width: 128px;
        height: 128px;
        margin-bottom: 20px;
    }
    
    .login-container h1 {
        color: #f7931a;
        margin-bottom: 20px;
    }
    
    .login-options {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }
    
    .eve-login-option {
        padding: 20px;
        background-color: #444;
        border-radius: 6px;
    }
    
    .eve-login-button {
        display: inline-block;
        margin: 20px 0;
        transition: transform 0.2s;
    }
    
    .eve-login-button:hover {
        transform: scale(1.05);
    }
    
    .login-note {
        font-size: 0.9em;
        color: #aaa;
        margin-top: 15px;
    }
    
    .admin-login-link {
        font-size: 0.9em;
        color: #aaa;
    }
    
    .admin-login-link a {
        color: #f7931a;
        text-decoration: none;
    }
    
    .admin-login-link a:hover {
        text-decoration: underline;
    }
</style>
{% endblock %}