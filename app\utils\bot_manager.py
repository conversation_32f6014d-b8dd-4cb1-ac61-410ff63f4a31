import threading
import discord
from discord import app_commands
from discord.ext import commands, tasks
import os
from dotenv import load_dotenv
from app.models.models import Topic
from app.extensions import db
import aiohttp
import asyncio
import json
import datetime
from typing import Dict, Optional, OrderedDict as OrderedDictType
from datetime import datetime, timezone, timedelta
import pytz
from collections import OrderedDict
import pickle
import os.path
import logging
import re

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("bot_debug.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('bot_manager')

# Add this for persistent storage of processed killmails
KILLMAIL_CACHE_FILE = "processed_killmails.pickle"

# Load environment variables
load_dotenv()

# Get Discord token from environment variables
DISCORD_TOKEN = os.getenv('DISCORD_TOKEN')
COMPLAINT_CHANNEL_ID = int(os.getenv('COMPLAINT_CHANNEL_ID'))
ADMIN_ROLE_ID = int(os.getenv('ADMIN_ROLE_ID'))
KILLMAIL_CHANNEL_ID = int(os.getenv('KILLMAIL_CHANNEL_ID'))
ALLIANCE_ID = os.getenv('ALLIANCE_ID')  # Add this to your .env file

# Add this near the top with other constants
ESI_STATUS_URL = "https://esi.evetech.net/latest/status/?datasource=tranquility"
STATUS_UPDATE_INTERVAL = 300  # 5 minutes in seconds

# Create bot instance with all intents
intents = discord.Intents.all()
bot = commands.Bot(command_prefix='!', intents=intents)

# Store Flask app reference
flask_app = None

# Cache for processed killmails (limit to 1000 entries)
# Load from disk if available
try:
    if os.path.exists(KILLMAIL_CACHE_FILE):
        with open(KILLMAIL_CACHE_FILE, 'rb') as f:
            processed_killmails = pickle.load(f)
            print(f"Loaded {len(processed_killmails)} killmails from cache file")
    else:
        processed_killmails = OrderedDict()
        print("No cache file found, starting with empty cache")
except Exception as e:
    print(f"Error loading killmail cache: {e}")
    processed_killmails = OrderedDict()

MAX_CACHE_SIZE = 5000  # Increased cache size
CACHE_SAVE_INTERVAL = 60  # Save cache to disk every 60 seconds

def save_killmail_cache():
    """Save the processed killmails cache to disk"""
    try:
        with open(KILLMAIL_CACHE_FILE, 'wb') as f:
            pickle.dump(processed_killmails, f)
        print(f"Saved {len(processed_killmails)} killmails to cache file")
    except Exception as e:
        print(f"Error saving killmail cache: {e}")

@tasks.loop(seconds=CACHE_SAVE_INTERVAL)
async def cache_save_task():
    """Task to periodically save the killmail cache to disk"""
    save_killmail_cache()

def init_flask_app(app):
    global flask_app
    flask_app = app

class ComplaintButtons(discord.ui.View):
    def __init__(self, complaint_id):
        super().__init__(timeout=None)
        self.complaint_id = complaint_id

    @discord.ui.button(label='Approve', style=discord.ButtonStyle.green, row=0)
    async def approve(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id == ADMIN_ROLE_ID for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to approve complaints.", ephemeral=True)
            return

        try:
            with flask_app.app_context():
                complaint = Topic.query.get(self.complaint_id)
                if complaint:
                    if complaint.status == 'approved':
                        await interaction.response.send_message("This complaint has already been approved.", ephemeral=True)
                        return
                    
                    complaint.status = 'approved'
                    complaint.updated_at = datetime.utcnow()
                    db.session.commit()
                    
                    # Disable all buttons after action
                    for child in self.children:
                        child.disabled = True
                    
                    await interaction.message.edit(view=self)
                    await interaction.response.send_message(f"Complaint #{self.complaint_id} has been approved!", ephemeral=True)
                    
                    # Update the embed to show the new status
                    embed = interaction.message.embeds[0]
                    embed.color = discord.Color.green()
                    embed.set_footer(text=f"Status: Approved by {interaction.user.display_name}")
                    await interaction.message.edit(embed=embed)
                else:
                    await interaction.response.send_message(f"Complaint #{self.complaint_id} not found.", ephemeral=True)
        except Exception as e:
            print(f"Error approving complaint via Discord: {str(e)}")
            await interaction.response.send_message(f"Error approving complaint: {str(e)}", ephemeral=True)

    @discord.ui.button(label='Deny', style=discord.ButtonStyle.red, row=0)
    async def deny(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not any(role.id == ADMIN_ROLE_ID for role in interaction.user.roles):
            await interaction.response.send_message("You don't have permission to deny complaints.", ephemeral=True)
            return

        try:
            with flask_app.app_context():
                complaint = Topic.query.get(self.complaint_id)
                if complaint:
                    if complaint.status == 'denied':
                        await interaction.response.send_message("This complaint has already been denied.", ephemeral=True)
                        return
                        
                    complaint.status = 'denied'
                    complaint.updated_at = datetime.utcnow()
                    db.session.commit()
                    
                    # Disable all buttons after action
                    for child in self.children:
                        child.disabled = True
                    
                    await interaction.message.edit(view=self)
                    await interaction.response.send_message(f"Complaint #{self.complaint_id} has been denied!", ephemeral=True)
                    
                    # Update the embed to show the new status
                    embed = interaction.message.embeds[0]
                    embed.color = discord.Color.red()
                    embed.set_footer(text=f"Status: Denied by {interaction.user.display_name}")
                    await interaction.message.edit(embed=embed)
                else:
                    await interaction.response.send_message(f"Complaint #{self.complaint_id} not found.", ephemeral=True)
        except Exception as e:
            print(f"Error denying complaint via Discord: {str(e)}")
            await interaction.response.send_message(f"Error denying complaint: {str(e)}", ephemeral=True)

async def send_complaint_to_discord(complaint_data):
    try:
        channel = bot.get_channel(COMPLAINT_CHANNEL_ID)
        if not channel:
            print(f"Could not find channel with ID {COMPLAINT_CHANNEL_ID}")
            return False

        embed = discord.Embed(
            title=f"New Complaint #{complaint_data['id']}: {complaint_data['title']}",
            description=complaint_data['message'],
            color=0xff9900,  # Orange color for pending status
            timestamp=datetime.utcnow()
        )
        
        # Add fields to embed
        embed.add_field(name="Author", value=complaint_data['author'], inline=True)
        embed.add_field(name="Type", value=complaint_data['complaint_type'], inline=True)
        embed.add_field(name="Blame", value=complaint_data['blame'], inline=True)
        embed.add_field(name="Rage Level", value=complaint_data['rage_level'], inline=True)
        embed.add_field(name="Quitting For", value="Yes" if complaint_data['quitting'] else "No", inline=True)
        embed.add_field(name="Reimbursement", value=complaint_data['reimbursement'], inline=True)
        
        if complaint_data['zkill_link']:
            embed.add_field(name="zKillboard Link", value=complaint_data['zkill_link'], inline=False)
            
        embed.set_footer(text="Status: Pending")

        # Create and send the message with buttons
        view = ComplaintButtons(complaint_data['id'])
        await channel.send(embed=embed, view=view)
        
        print(f"Successfully sent complaint #{complaint_data['id']} to Discord")
        return True

    except Exception as e:
        print(f"Error sending complaint to Discord: {str(e)}")
        return False

def queue_discord_message(complaint_data: Dict) -> bool:
    """
    Queues a complaint message to be sent to Discord.
    Returns True if successfully queued, False otherwise.
    """
    try:
        # Create a coroutine and run it in the bot's event loop
        future = asyncio.run_coroutine_threadsafe(
            send_complaint_to_discord(complaint_data),
            bot.loop
        )
        # Wait for the result with a timeout
        return future.result(timeout=10)
    except Exception as e:
        print(f"Error queuing Discord message: {str(e)}")
        return False

# Cache for EVE Online type IDs and names
type_cache: Dict[int, str] = {}
character_cache: Dict[int, str] = {}
corporation_cache: Dict[int, str] = {}
alliance_cache: Dict[int, str] = {}
system_cache: Dict[int, str] = {}

async def fetch_esi_data(url: str) -> Optional[dict]:
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"https://esi.evetech.net/latest/{url}") as response:
                if response.status == 200:
                    return await response.json()
    except Exception as e:
        print(f"ESI request failed for {url}: {e}")
    return None

async def get_type_name(type_id: int) -> str:
    if type_id in type_cache:
        return type_cache[type_id]
    
    data = await fetch_esi_data(f"universe/types/{type_id}/")
    if data and 'name' in data:
        type_cache[type_id] = data['name']
        return data['name']
    return 'Unknown'

async def get_character_name(character_id: int) -> str:
    if character_id in character_cache:
        return character_cache[character_id]
    
    data = await fetch_esi_data(f"characters/{character_id}/")
    if data and 'name' in data:
        character_cache[character_id] = data['name']
        return data['name']
    return 'Unknown'

async def get_corporation_name(corporation_id: int) -> str:
    if corporation_id in corporation_cache:
        return corporation_cache[corporation_id]
    
    data = await fetch_esi_data(f"corporations/{corporation_id}/")
    if data and 'name' in data:
        corporation_cache[corporation_id] = data['name']
        return data['name']
    return 'Unknown Corp'

async def get_alliance_name(alliance_id: int) -> str:
    if alliance_id in alliance_cache:
        return alliance_cache[alliance_id]
    
    data = await fetch_esi_data(f"alliances/{alliance_id}/")
    if data and 'name' in data:
        alliance_cache[alliance_id] = data['name']
        return data['name']
    return 'No Alliance'

async def get_system_name(system_id: int) -> str:
    if system_id in system_cache:
        return system_cache[system_id]
    
    data = await fetch_esi_data(f"universe/systems/{system_id}/")
    if data and 'name' in data:
        system_cache[system_id] = data['name']
        return data['name']
    return 'Unknown'

async def process_killmail(killmail_data):
    try:
        logger.info("Processing killmail...")
        
        # Extract killmail ID early
        killmail_id = killmail_data.get('package', {}).get('killmail', {}).get('killmail_id')
        if not killmail_id:
            logger.error("ERROR: No killmail ID found in data")
            return
        
        # Get timestamp from killmail
        killmail_time_str = killmail_data.get('package', {}).get('killmail', {}).get('killmail_time')
        if not killmail_time_str:
            logger.error(f"ERROR: No timestamp for killmail {killmail_id}")
            killmail_time = datetime.now(timezone.utc)
        else:
            killmail_time = datetime.fromisoformat(killmail_time_str.replace('Z', '+00:00'))
        
        # More robust duplicate check with logging
        if killmail_id in processed_killmails:
            logger.warning(f"DUPLICATE: Skipping already processed killmail {killmail_id}")
            return
        
        # Check if killmail is too old (more than 1 hour)
        if (datetime.now(timezone.utc) - killmail_time) > timedelta(hours=1):
            logger.info(f"OLD KILLMAIL: Skipping killmail {killmail_id} from {killmail_time}")
            processed_killmails[killmail_id] = datetime.now(timezone.utc).timestamp()
            return
            
        # Add to processed cache BEFORE processing to prevent race conditions
        processed_killmails[killmail_id] = datetime.now(timezone.utc).timestamp()
        logger.info(f"Added killmail {killmail_id} to processed cache. Cache size: {len(processed_killmails)}")
        
        # Maintain cache size
        while len(processed_killmails) > MAX_CACHE_SIZE:
            oldest = processed_killmails.popitem(last=False)
            logger.info(f"Removed oldest killmail {oldest[0]} from cache")
        
        # Save cache immediately after adding new killmail
        save_killmail_cache()
        
        channel = bot.get_channel(KILLMAIL_CHANNEL_ID)
        if not channel:
            print(f"ERROR: Could not find killmail channel with ID {KILLMAIL_CHANNEL_ID}")
            return
        print(f"Found killmail channel: {channel.name}")

        if not killmail_data or 'package' not in killmail_data or not killmail_data['package']:
            print("ERROR: Invalid killmail data received")
            return

        package = killmail_data['package']
        killmail = package.get('killmail', {})
        
        # Debug alliance involvement
        victim = killmail.get('victim', {})
        attackers = killmail.get('attackers', [])
        victim_alliance = str(victim.get('alliance_id'))
        print(f"Victim alliance: {victim_alliance}, Our alliance: {ALLIANCE_ID}")
        print(f"Number of attackers: {len(attackers)}")
        
        # Check if our alliance is the victim or among the attackers
        is_victim = victim_alliance == ALLIANCE_ID
        is_attacker = any(str(attacker.get('alliance_id')) == ALLIANCE_ID for attacker in attackers)
        
        print(f"Is victim: {is_victim}, Is attacker: {is_attacker}")
        
        # Skip if our alliance is not involved
        if not (is_victim or is_attacker):
            print("Our alliance not involved, skipping...")
            return
            
        zkb = package.get('zkb', {})
        ship_type_id = victim.get('ship_type_id', 0)
        
        # Parse timestamp and convert to EST
        killmail_time = datetime.fromisoformat(killmail.get('killmail_time').replace('Z', '+00:00'))
        est_tz = pytz.timezone('America/New_York')
        est_time = killmail_time.astimezone(est_tz)
        formatted_date = est_time.strftime('%Y-%m-%d %I:%M:%S %p EST')
        
        # Fetch names from ESI
        ship_name = await get_type_name(ship_type_id)
        victim_name = await get_character_name(victim.get('character_id', 0))
        victim_corp = await get_corporation_name(victim.get('corporation_id', 0))
        victim_alliance_name = await get_alliance_name(victim.get('alliance_id', 0))
        system_name = await get_system_name(killmail.get('solar_system_id', 0))

        # Create embed
        zkill_link = f"https://zkillboard.com/kill/{killmail.get('killmail_id')}/"
        ship_icon_url = f"https://images.evetech.net/types/{ship_type_id}/render?size=128"
        
        embed = discord.Embed(
            title=f"Killmail: {ship_name} - {formatted_date}",
            url=zkill_link,
            color=0xff0000 if str(victim.get('alliance_id')) == ALLIANCE_ID else 0x00ff00,
            timestamp=killmail_time
        )
        
        # Set the ship icon as the thumbnail
        embed.set_thumbnail(url=ship_icon_url)

        # Add victim details
        victim_info = (
            f"**Pilot:** {victim_name}\n"
            f"**Corp:** {victim_corp}\n"
            f"**Alliance:** {victim_alliance_name}\n"
            f"**Ship:** {ship_name}"
        )
        embed.add_field(name="Victim", value=victim_info, inline=False)

        # Add value
        try:
            value = float(zkb.get('totalValue', 0))
            formatted_value = "{:,.2f}".format(value)
        except (ValueError, TypeError):
            formatted_value = "Unknown"
        embed.add_field(name="Value", value=f"{formatted_value} ISK", inline=True)

        # Add system info
        embed.add_field(name="Location", value=f"{system_name}", inline=True)

        # Add final blow info
        final_blow = next((a for a in killmail.get('attackers', []) if a.get('final_blow')), None)
        if final_blow:
            final_blow_name = await get_character_name(final_blow.get('character_id', 0))
            final_blow_ship = await get_type_name(final_blow.get('ship_type_id', 0))
            final_blow_info = f"**Pilot:** {final_blow_name}\n**Ship:** {final_blow_ship}"
            embed.add_field(name="Final Blow", value=final_blow_info, inline=True)

        # Send to Discord
        channel = bot.get_channel(KILLMAIL_CHANNEL_ID)
        if channel:
            await channel.send(embed=embed)

    except Exception as e:
        print(f"Error processing killmail: {str(e)}")
        print(f"Killmail data: {json.dumps(killmail_data, indent=2)}")

async def zkill_redisq_loop():
    logger.info(f"Starting zKill feed loop... Alliance ID: {ALLIANCE_ID}")
    url = "https://redisq.zkillboard.com/listen.php"
    
    start_time = datetime.now(timezone.utc)
    logger.info(f"Feed start time: {start_time}")
    
    # Use a more unique queueID to avoid getting duplicate killmails
    unique_id = f"SkydaddyRanch_{ALLIANCE_ID}_{int(start_time.timestamp())}"
    logger.info(f"Using unique queue ID: {unique_id}")
    
    params = {
        "queueID": unique_id,
        "ttw": 10,  # Time to wait
    }
    
    # Track processed killmails in this session
    session_processed = set()
    duplicate_count = 0
    request_count = 0
    
    async with aiohttp.ClientSession() as session:
        while True:
            try:
                request_count += 1
                if request_count % 100 == 0:
                    logger.info(f"Made {request_count} requests to zKill, found {duplicate_count} duplicates")
                
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data and data.get('package'):
                            # Extract killmail ID for session tracking
                            killmail_id = data.get('package', {}).get('killmail', {}).get('killmail_id')
                            if killmail_id:
                                # Triple-layer duplicate check
                                if killmail_id in session_processed:
                                    logger.warning(f"SESSION DUPLICATE: Already processed killmail {killmail_id} in this session")
                                    duplicate_count += 1
                                    await asyncio.sleep(1)
                                    continue
                                
                                if killmail_id in processed_killmails:
                                    logger.warning(f"CACHE DUPLICATE: Already processed killmail {killmail_id} in persistent cache")
                                    duplicate_count += 1
                                    # Still add to session cache to prevent reprocessing
                                    session_processed.add(killmail_id)
                                    await asyncio.sleep(1)
                                    continue
                                    
                                session_processed.add(killmail_id)
                                logger.info(f"New killmail {killmail_id} received from zKill")
                                await process_killmail(data)
                            else:
                                logger.warning("Received data but no killmail ID found")
                        else:
                            # No need to log every empty response
                            await asyncio.sleep(1)  # Sleep if no data
                    else:
                        logger.error(f"RedisQ request failed with status: {response.status}")
                        await asyncio.sleep(30)  # Longer sleep on error
            except Exception as e:
                logger.exception(f"Error in zkill_redisq_loop: {str(e)}")
                await asyncio.sleep(30)  # Longer sleep on error

def run_discord_bot():
    if not DISCORD_TOKEN:
        raise ValueError("Discord token not found in environment variables")
    
    # Make sure we save the cache when shutting down
    try:
        logger.info("Starting Discord bot...")
        bot.run(DISCORD_TOKEN)
    except KeyboardInterrupt:
        logger.info("Bot shutting down, saving cache...")
        save_killmail_cache()
    except Exception as e:
        logger.exception(f"Error running Discord bot: {str(e)}")
        save_killmail_cache()

def initialize_bot():
    bot_thread = threading.Thread(target=run_discord_bot)
    bot_thread.daemon = True
    bot_thread.start()
    return bot_thread

async def get_eve_player_count() -> str:
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(ESI_STATUS_URL) as response:
                if response.status == 200:
                    data = await response.json()
                    return f"EVE Online: {data.get('players', '0')} "
    except Exception as e:
        print(f"Error fetching player count: {e}")
        return "EVE Online"

@tasks.loop(seconds=STATUS_UPDATE_INTERVAL)
async def update_bot_status():
    try:
        status_text = await get_eve_player_count()
        await bot.change_presence(
            status=discord.Status.online,
            activity=discord.Game(name=status_text)
        )
    except Exception as e:
        print(f"Error updating bot status: {e}")

# Add this variable to track if zkill loop is already running
_zkill_loop_running = False

# Add this class for the timer command
class TimerModal(discord.ui.Modal):
    def __init__(self):
        super().__init__(title="Create Timer")
        
        # Event name input
        self.name = discord.ui.TextInput(
            label="Event Name",
            placeholder="Enter event name",
            required=True
        )
        
        # Time input
        self.time = discord.ui.TextInput(
            label="Time",
            placeholder="e.g. 'Friday 9pm ET', 'tomorrow 8pm PT', '2 days 3 hours'",
            required=True
        )
        
        # Fleet Commander input
        self.fc = discord.ui.TextInput(
            label="Fleet Commander",
            placeholder="Enter FC name",
            required=False
        )
        
        # Doctrine input
        self.doctrine = discord.ui.TextInput(
            label="Doctrine",
            placeholder="Enter fleet doctrine",
            required=False
        )
        
        # Add items to modal
        self.add_item(self.name)
        self.add_item(self.time)
        self.add_item(self.fc)
        self.add_item(self.doctrine)

    async def on_submit(self, interaction: discord.Interaction):
        try:
            # Parse the time input using natural language
            event_time = parse_time_input(self.time.value)
            
            if not event_time:
                await interaction.response.send_message(
                    "I couldn't understand that time format. Try something like:\n"
                    "• 'friday 9pm' (assumes ET timezone)\n"
                    "• 'tomorrow 8pm PT'\n"
                    "• 'today 7pm'\n"
                    "• '9pm' (assumes today or tomorrow)\n"
                    "• '2 days 3 hours'\n"
                    "• 'YYYY-MM-DD HH:MM'", 
                    ephemeral=True
                )
                return
            
            # Create the timer embed
            embed = discord.Embed(
                title=f"⏰ Timer: {self.name.value}",
                description=f"Event starts <t:{int(event_time.timestamp())}:R>",
                color=0x3498db
            )
            
            # Add field for local time - Discord will automatically convert to each user's local time
            embed.add_field(name="Local Time", value=f"<t:{int(event_time.timestamp())}:F>", inline=False)
            
            # Add FC and doctrine if provided
            fc_name = self.fc.value.strip() if self.fc.value else None
            doctrine_name = self.doctrine.value.strip() if self.doctrine.value else None
            
            embed.set_footer(text=f"Created by {interaction.user.display_name}")
            
            # Send the message with the activity type and importance selectors
            view = TimerOptionsView(
                interaction.user, 
                self.name.value, 
                event_time,
                fc_name,
                doctrine_name
            )
            await interaction.response.send_message(embed=embed, view=view)
            
        except Exception as e:
            logger.error(f"Error creating timer: {str(e)}")
            await interaction.response.send_message(
                "There was an error processing your time. Try something like:\n"
                "• 'friday 9pm' (assumes ET timezone)\n"
                "• 'tomorrow 8pm PT'\n"
                "• 'today 7pm'\n"
                "• '9pm' (assumes today or tomorrow)\n"
                "• '2 days 3 hours'\n"
                "• 'YYYY-MM-DD HH:MM'", 
                ephemeral=True
            )

def parse_time_input(time_input):
    """Parse various time formats into a datetime object"""
    if not time_input:
        logger.warning("Empty time input received")
        return None
        
    time_input = time_input.lower().strip()
    logger.info(f"Parsing time input: '{time_input}'")
    
    # Define timezone mappings
    timezone_map = {
        'et': 'America/New_York',      # Eastern Time
        'est': 'America/New_York',
        'edt': 'America/New_York',
        'ct': 'America/Chicago',       # Central Time
        'cst': 'America/Chicago',
        'cdt': 'America/Chicago',
        'mt': 'America/Denver',        # Mountain Time
        'mst': 'America/Denver',
        'mdt': 'America/Denver',
        'pt': 'America/Los_Angeles',   # Pacific Time
        'pst': 'America/Los_Angeles',
        'pdt': 'America/Los_Angeles',
        'utc': 'UTC',                  # UTC
        'gmt': 'UTC'
    }
    
    # Default timezone (Eastern Time)
    default_tz = pytz.timezone('America/New_York')
    
    # Extract timezone if specified
    specified_tz = default_tz
    for tz_code, tz_name in timezone_map.items():
        if f" {tz_code}" in time_input:
            specified_tz = pytz.timezone(tz_name)
            time_input = time_input.replace(f" {tz_code}", "")
            logger.info(f"Detected timezone: {tz_code} -> {tz_name}")
            break
    
    # Get current time in the specified timezone
    tz_now = datetime.now(specified_tz)
    logger.info(f"Current time in {specified_tz.zone}: {tz_now}")
    
    # Try to parse as exact date/time first (YYYY-MM-DD HH:MM)
    try:
        if re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$', time_input):
            dt = datetime.strptime(time_input, "%Y-%m-%d %H:%M")
            # Apply the specified timezone
            dt = specified_tz.localize(dt)
            # Convert to UTC
            return dt.astimezone(timezone.utc)
    except ValueError:
        pass
    
    # Try to parse day name with time (e.g., "Friday 9pm" or "Monday 15:30")
    day_pattern = r'(mon|tue|wed|thu|fri|sat|sun|monday|tuesday|wednesday|thursday|friday|saturday|sunday)s*\s+(\d{1,2})(?::(\d{1,2}))?\s*(am|pm)?'
    day_match = re.search(day_pattern, time_input)
    
    if day_match:
        logger.info(f"Day pattern matched: {day_match.groups()}")
        day_name, hour, minute, am_pm = day_match.groups()
        
        # Map abbreviated day names to full names
        day_abbr_map = {
            'mon': 'monday', 'tue': 'tuesday', 'wed': 'wednesday', 'thu': 'thursday',
            'fri': 'friday', 'sat': 'saturday', 'sun': 'sunday'
        }
        
        # Convert abbreviated day name to full name if needed
        if day_name in day_abbr_map:
            day_name = day_abbr_map[day_name]
        
        # Convert day name to day number (0 = Monday, 6 = Sunday)
        day_map = {
            'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
            'friday': 4, 'saturday': 5, 'sunday': 6
        }
        target_day = day_map[day_name]
        
        # Get current day of week in the specified timezone
        current_day = tz_now.weekday()
        
        # Calculate days to add
        days_to_add = (target_day - current_day) % 7
        
        # Parse the time
        hour = int(hour)
        minute = int(minute) if minute else 0
        
        # Default to PM for hours 1-11 if not specified
        if not am_pm and 1 <= hour <= 11:
            am_pm = 'pm'
        
        # Adjust for AM/PM
        if am_pm and am_pm.lower() == 'pm' and hour < 12:
            hour += 12
        elif am_pm and am_pm.lower() == 'am' and hour == 12:
            hour = 0
            
        # If it's the same day, check if the time has already passed
        if days_to_add == 0:
            if (hour < tz_now.hour) or (hour == tz_now.hour and minute < tz_now.minute):
                # If the time has already passed today, move to next week
                days_to_add = 7
        
        # Create the target date in the specified timezone
        target_date = tz_now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=days_to_add)
        target_date = target_date.replace(hour=hour, minute=minute)
        
        # Convert to UTC
        utc_date = target_date.astimezone(timezone.utc)
        
        logger.info(f"Parsed day '{day_name}' in {specified_tz.zone} to: {target_date}, UTC: {utc_date}")
        return utc_date
    
    # Try a simpler day pattern as fallback
    simple_day_pattern = r'(mon|tue|wed|thu|fri|sat|sun|monday|tuesday|wednesday|thursday|friday|saturday|sunday)\s*(\d{1,2})\s*(am|pm)'
    simple_day_match = re.search(simple_day_pattern, time_input)
    
    if simple_day_match:
        logger.info(f"Simple day pattern matched: {simple_day_match.groups()}")
        day_name, hour, am_pm = simple_day_match.groups()
        
        # Map abbreviated day names to full names
        day_abbr_map = {
            'mon': 'monday', 'tue': 'tuesday', 'wed': 'wednesday', 'thu': 'thursday',
            'fri': 'friday', 'sat': 'saturday', 'sun': 'sunday'
        }
        
        # Convert abbreviated day name to full name if needed
        if day_name in day_abbr_map:
            day_name = day_abbr_map[day_name]
        
        # Convert day name to day number (0 = Monday, 6 = Sunday)
        day_map = {
            'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
            'friday': 4, 'saturday': 5, 'sunday': 6
        }
        target_day = day_map[day_name]
        
        # Get current day of week in the specified timezone
        tz_now = datetime.now(specified_tz)
        current_day = tz_now.weekday()
        
        # Calculate days to add
        days_to_add = (target_day - current_day) % 7
        
        # Parse the time
        hour = int(hour)
        minute = 0  # No minutes in this simple pattern
        
        # Adjust for AM/PM
        if am_pm.lower() == 'pm' and hour < 12:
            hour += 12
        elif am_pm.lower() == 'am' and hour == 12:
            hour = 0
            
        # If it's the same day, check if the time has already passed
        if days_to_add == 0:
            if (hour < tz_now.hour) or (hour == tz_now.hour and minute < tz_now.minute):
                # If the time has already passed today, move to next week
                days_to_add = 7
        
        # Create the target date in the specified timezone
        target_date = tz_now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=days_to_add)
        target_date = target_date.replace(hour=hour, minute=minute)
        
        # Convert to UTC
        utc_date = target_date.astimezone(timezone.utc)
        
        logger.info(f"Parsed simple day '{day_name}' in {specified_tz.zone} to: {target_date}, UTC: {utc_date}")
        return utc_date
    
    # Try to parse "tomorrow" with time
    tomorrow_pattern = r'(tom|tmr|tomorrow|tmrw)\s*(\d{1,2})(?::(\d{1,2}))?\s*(am|pm)?'
    tomorrow_match = re.match(tomorrow_pattern, time_input)
    
    if tomorrow_match:
        _, hour, minute, am_pm = tomorrow_match.groups()
        
        # Get current time in the specified timezone
        tz_now = datetime.now(specified_tz)
        
        # Create tomorrow's date in the specified timezone
        target_date = tz_now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
        
        # Parse the time
        hour = int(hour)
        minute = int(minute) if minute else 0
        
        # Default to PM for hours 1-11 if not specified
        if not am_pm and 1 <= hour <= 11:
            am_pm = 'pm'
        
        # Adjust for AM/PM
        if am_pm and am_pm.lower() == 'pm' and hour < 12:
            hour += 12
        elif am_pm and am_pm.lower() == 'am' and hour == 12:
            hour = 0
        
        # Set the time
        target_date = target_date.replace(hour=hour, minute=minute)
        
        # Convert to UTC
        utc_date = target_date.astimezone(timezone.utc)
        
        logger.info(f"Parsed 'tomorrow' in {specified_tz.zone} to: {target_date}, UTC: {utc_date}")
        return utc_date
    
    # Try to parse "today" with time
    today_pattern = r'(today|tod)\s*(\d{1,2})(?::(\d{1,2}))?\s*(am|pm)?'
    today_match = re.match(today_pattern, time_input)
    
    if today_match:
        _, hour, minute, am_pm = today_match.groups()
        
        # Get current time in the specified timezone
        tz_now = datetime.now(specified_tz)
        
        # Create today's date with time set to 00:00
        target_date = tz_now.replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Parse the time
        hour = int(hour)
        minute = int(minute) if minute else 0
        
        # Default to PM for hours 1-11 if not specified
        if not am_pm and 1 <= hour <= 11:
            am_pm = 'pm'
        
        # Adjust for AM/PM
        if am_pm and am_pm.lower() == 'pm' and hour < 12:
            hour += 12
        elif am_pm and am_pm.lower() == 'am' and hour == 12:
            hour = 0
        
        # Set the time
        target_date = target_date.replace(hour=hour, minute=minute)
        
        # If the time has already passed today, show an error
        if target_date < tz_now:
            logger.warning(f"Specified time {target_date} has already passed")
            return None
        
        # Convert to UTC
        utc_date = target_date.astimezone(timezone.utc)
        
        logger.info(f"Parsed 'today' in {specified_tz.zone} to: {target_date}, UTC: {utc_date}")
        return utc_date
    
    # Try to parse just a time (assume today)
    time_only_pattern = r'^(\d{1,2})(?::(\d{1,2}))?\s*(am|pm)?$'
    time_only_match = re.match(time_only_pattern, time_input)
    
    if time_only_match:
        hour, minute, am_pm = time_only_match.groups()
        
        # Get current time in the specified timezone
        tz_now = datetime.now(specified_tz)
        
        # Create today's date with time set to 00:00
        target_date = tz_now.replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Parse the time
        hour = int(hour)
        minute = int(minute) if minute else 0
        
        # Default to PM for hours 1-11 if not specified
        if not am_pm and 1 <= hour <= 11:
            am_pm = 'pm'
        
        # Adjust for AM/PM
        if am_pm and am_pm.lower() == 'pm' and hour < 12:
            hour += 12
        elif am_pm and am_pm.lower() == 'am' and hour == 12:
            hour = 0
        
        # Set the time
        target_date = target_date.replace(hour=hour, minute=minute)
        
        # If the time has already passed today, move to tomorrow
        if target_date < tz_now:
            target_date += timedelta(days=1)
        
        # Convert to UTC
        utc_date = target_date.astimezone(timezone.utc)
        
        logger.info(f"Parsed time-only '{time_input}' in {specified_tz.zone} to: {target_date}, UTC: {utc_date}")
        return utc_date
    
    # Parse relative time expressions
    total_seconds = 0
    time_pattern = r'(\d+)\s*(d|day|days|h|hour|hours|m|min|mins|minute|minutes)'
    matches = re.findall(time_pattern, time_input)
    
    for value, unit in matches:
        value = int(value)
        if unit.startswith('d'):  # d, day, days
            total_seconds += value * 86400
        elif unit.startswith('h'):  # h, hour, hours
            total_seconds += value * 3600
        elif unit.startswith('m'):  # m, min, mins, minute, minutes
            total_seconds += value * 60
    
    # If we found time components, calculate the future time
    if total_seconds > 0:
        # Use the specified timezone as reference
        tz_now = datetime.now(specified_tz)
        future_time = tz_now + timedelta(seconds=total_seconds)
        
        # Convert to UTC
        utc_future_time = future_time.astimezone(timezone.utc)
        
        logger.info(f"Parsed '{time_input}' to {total_seconds} seconds from now in {specified_tz.zone}. Future: {future_time}, UTC: {utc_future_time}")
        return utc_future_time
    
    # Last resort: Try a very simple pattern for day + hour
    very_simple_pattern = r'(mon|tue|wed|thu|fri|sat|sun|monday|tuesday|wednesday|thursday|friday|saturday|sunday)\s+(\d{1,2})'
    very_simple_match = re.search(very_simple_pattern, time_input)
    
    if very_simple_match:
        logger.info(f"Very simple pattern matched: {very_simple_match.groups()}")
        day_name, hour = very_simple_match.groups()
        
        # Map abbreviated day names to full names
        day_abbr_map = {
            'mon': 'monday', 'tue': 'tuesday', 'wed': 'wednesday', 'thu': 'thursday',
            'fri': 'friday', 'sat': 'saturday', 'sun': 'sunday'
        }
        
        # Convert abbreviated day name to full name if needed
        if day_name in day_abbr_map:
            day_name = day_abbr_map[day_name]
        
        # Convert day name to day number (0 = Monday, 6 = Sunday)
        day_map = {
            'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
            'friday': 4, 'saturday': 5, 'sunday': 6
        }
        target_day = day_map[day_name]
        
        # Get current day of week in the specified timezone
        tz_now = datetime.now(specified_tz)
        current_day = tz_now.weekday()
        
        # Calculate days to add
        days_to_add = (target_day - current_day) % 7
        
        # Parse the time - assume PM for hours 1-11
        hour = int(hour)
        minute = 0
        
        # Default to PM for hours 1-11
        if 1 <= hour <= 11:
            hour += 12
        
        # If it's the same day, check if the time has already passed
        if days_to_add == 0:
            if (hour < tz_now.hour) or (hour == tz_now.hour and minute < tz_now.minute):
                # If the time has already passed today, move to next week
                days_to_add = 7
        
        # Create the target date in the specified timezone
        target_date = tz_now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=days_to_add)
        target_date = target_date.replace(hour=hour, minute=minute)
        
        # Convert to UTC
        utc_date = target_date.astimezone(timezone.utc)
        
        logger.info(f"Parsed very simple day '{day_name}' in {specified_tz.zone} to: {target_date}, UTC: {utc_date}")
        return utc_date
    
    logger.warning(f"Could not parse time input: '{time_input}'")
    return None

class TimerOptionsView(discord.ui.View):
    def __init__(self, author, event_name, event_time, fc_name=None, doctrine_name=None):
        super().__init__(timeout=120)
        self.author = author
        self.event_name = event_name
        self.event_time = event_time
        self.fc_name = fc_name
        self.doctrine_name = doctrine_name
        self.activity_types = []
        self.importance = "Medium"
        self.reping_time = "none"
        
        # Create a more compact layout with more activity options
        # First row: Common activity checkboxes
        self.add_item(ActivityCheckbox("PVP"))
        self.add_item(ActivityCheckbox("ROAM"))
        self.add_item(ActivityCheckbox("BASH"))
        self.add_item(ActivityCheckbox("SRP"))
        
        # Second row: More activity checkboxes
        self.add_item(ActivityCheckbox("MINING", row=1))
        self.add_item(ActivityCheckbox("RATTING", row=1))
        self.add_item(ActivityCheckbox("INDUSTRY", row=1))
        self.add_item(ActivityCheckbox("OTHER", row=1))
        
        # Third row: Importance selector
        self.add_item(ImportanceSelect(row=2))
        
        # Fourth row: Re-ping selector
        self.add_item(RePingSelect(row=3))
        
        # Fifth row: Confirm button
        self.add_item(ConfirmButton(row=4))
    
    def get_updated_embed(self):
        """Create an updated embed that shows the current selections"""
        # Get selected activities
        selected_activities = []
        for item in self.children:
            if isinstance(item, ActivityCheckbox) and item.value:
                selected_activities.append(item.label)
        
        # Create the embed
        embed = discord.Embed(
            title=f"⏰ Timer: {self.event_name}",
            description=f"Event starts <t:{int(self.event_time.timestamp())}:R>",
            color=0x3498db
        )
        
        # Add field for local time
        embed.add_field(name="Local Time", value=f"<t:{int(self.event_time.timestamp())}:F>", inline=False)
        
        # Add FC and doctrine if provided
        if self.fc_name:
            embed.add_field(name="Fleet Commander", value=self.fc_name, inline=True)
        
        if self.doctrine_name:
            embed.add_field(name="Doctrine", value=self.doctrine_name, inline=True)
        
        # Add current selections
        if selected_activities:
            embed.add_field(name="Activities", value=", ".join(selected_activities), inline=True)
        
        embed.add_field(name="Importance", value=self.importance, inline=True)
        
        reping_labels = {
            "none": "None",
            "15min": "15 Minutes Before",
            "30min": "30 Minutes Before",
            "1hour": "1 Hour Before"
        }
        embed.add_field(name="Re-ping", value=reping_labels.get(self.reping_time, "None"), inline=True)
        
        # Set color based on importance
        if self.importance == "High":
            embed.color = 0xe74c3c  # Red
        elif self.importance == "Medium":
            embed.color = 0x3498db  # Blue
        else:  # Low
            embed.color = 0x2ecc71  # Green
        
        embed.set_footer(text=f"Created by {self.author.display_name}")
        
        return embed

class ActivityCheckbox(discord.ui.Button):
    def __init__(self, label, row=0):
        super().__init__(
            label=label,
            style=discord.ButtonStyle.secondary,
            custom_id=f"activity_{label.lower()}",
            row=row
        )
        self.value = False
    
    async def callback(self, interaction: discord.Interaction):
        # Only allow the author to interact with buttons
        if interaction.user.id != self.view.author.id:
            await interaction.response.send_message("Only the timer creator can modify these settings.", ephemeral=True)
            return
            
        # Toggle the value
        self.value = not self.value
        
        # Change the style based on selection
        self.style = discord.ButtonStyle.success if self.value else discord.ButtonStyle.secondary
        
        # Update the embed to show the current selections
        updated_embed = self.view.get_updated_embed()
        
        # Update the message
        await interaction.response.edit_message(embed=updated_embed, view=self.view)

class ImportanceSelect(discord.ui.Select):
    def __init__(self, row=None):
        options = [
            discord.SelectOption(
                label="High Importance",
                description="Will ping @here on reminder",
                value="High",
                emoji="🔴"
            ),
            discord.SelectOption(
                label="Medium Importance",
                description="Standard importance",
                value="Medium",
                emoji="🔵",
                default=True
            ),
            discord.SelectOption(
                label="Low Importance",
                description="Optional event",
                value="Low",
                emoji="🟢"
            )
        ]
        
        super().__init__(
            placeholder="Select Importance",
            min_values=1,
            max_values=1,
            options=options,
            custom_id="importance_select",
            row=row
        )
    
    async def callback(self, interaction: discord.Interaction):
        # Only allow the author to interact with selects
        if interaction.user.id != self.view.author.id:
            await interaction.response.send_message("Only the timer creator can modify these settings.", ephemeral=True)
            return
        
        # Store the selected importance in the view
        self.view.importance = self.values[0] if self.values else "Medium"
        
        # Update the embed to show the current selections
        updated_embed = self.view.get_updated_embed()
        
        # Update the message with the new embed
        await interaction.response.edit_message(embed=updated_embed, view=self.view)

class RePingSelect(discord.ui.Select):
    def __init__(self, row=None):
        options = [
            discord.SelectOption(
                label="No Re-ping",
                description="No reminder before event",
                value="none",
                emoji="❌",
                default=True
            ),
            discord.SelectOption(
                label="15 Minutes Before",
                description="Reminder 15 minutes before",
                value="15min",
                emoji="⏱️"
            ),
            discord.SelectOption(
                label="30 Minutes Before",
                description="Reminder 30 minutes before",
                value="30min",
                emoji="⏱️"
            ),
            discord.SelectOption(
                label="1 Hour Before",
                description="Reminder 1 hour before",
                value="1hour",
                emoji="⏱️"
            )
        ]
        
        super().__init__(
            placeholder="Select Re-ping Time",
            min_values=1,
            max_values=1,
            options=options,
            custom_id="reping_select",
            row=row
        )
    
    async def callback(self, interaction: discord.Interaction):
        # Only allow the author to interact with selects
        if interaction.user.id != self.view.author.id:
            await interaction.response.send_message("Only the timer creator can modify these settings.", ephemeral=True)
            return
        
        # Store the selected re-ping time in the view
        self.view.reping_time = self.values[0] if self.values else "none"
        
        # Update the embed to show the current selections
        updated_embed = self.view.get_updated_embed()
        
        # Update the message with the new embed
        await interaction.response.edit_message(embed=updated_embed, view=self.view)

class ConfirmButton(discord.ui.Button):
    def __init__(self, row=None):
        super().__init__(
            style=discord.ButtonStyle.primary,
            label="Confirm",
            custom_id="confirm_timer",
            row=row  # Pass row parameter to parent constructor
        )
    
    async def callback(self, interaction: discord.Interaction):
        view = self.view
        
        # Check if user is the author
        if interaction.user.id != view.author.id:
            await interaction.response.send_message("Only the timer creator can confirm this timer.", ephemeral=True)
            return
        
        # Collect selected activities
        selected_activities = []
        for item in view.children:
            if isinstance(item, ActivityCheckbox) and item.value:
                selected_activities.append(item.label)
        
        # Get importance level
        importance = "Medium"  # Default
        for item in view.children:
            if isinstance(item, ImportanceSelect):
                importance = item.values[0] if item.values else "Medium"
        
        # Get re-ping time
        reping_value = "none"  # Default
        for item in view.children:
            if isinstance(item, RePingSelect):
                reping_value = item.values[0] if item.values else "none"
        
        # Create the final embed
        embed = discord.Embed(
            title=f"⏰ Timer: {view.event_name}",
            description=f"Event starts <t:{int(view.event_time.timestamp())}:R>",
            color=0x3498db
        )
        
        # Add field for local time - Discord will automatically convert to each user's local time
        embed.add_field(name="Local Time", value=f"<t:{int(view.event_time.timestamp())}:F>", inline=False)
        
        # Add FC and doctrine if provided
        if view.fc_name:
            embed.add_field(name="Fleet Commander", value=view.fc_name, inline=True)
        
        if view.doctrine_name:
            embed.add_field(name="Doctrine", value=view.doctrine_name, inline=True)
        
        if selected_activities:
            embed.add_field(name="Activities", value=", ".join(selected_activities), inline=True)
        
        embed.add_field(name="Importance", value=importance, inline=True)
        
        # Set color based on importance
        if importance == "High":
            embed.color = 0xe74c3c  # Red
        elif importance == "Medium":
            embed.color = 0x3498db  # Blue
        else:  # Low
            embed.color = 0x2ecc71  # Green
        
        embed.set_footer(text=f"Created by {view.author.display_name}")
        
        # Disable all buttons
        for child in view.children:
            child.disabled = True
        
        # Update the message
        await interaction.response.edit_message(embed=embed, view=view)
        
        # Schedule reminder if needed
        if reping_value != "none":
            await schedule_reping(
                interaction,
                interaction.message,
                view.event_name,
                view.event_time,
                reping_value,
                selected_activities,
                importance,
                view.fc_name,
                view.doctrine_name
            )

async def schedule_reping(
    interaction, 
    message, 
    event_name, 
    event_time, 
    reping_time, 
    activities, 
    importance,
    fc_name=None,
    doctrine_name=None
):
    """Schedule a reminder ping for the event"""
    # Calculate when to send the reminder
    reminder_times = {
        "15min": 15 * 60,
        "30min": 30 * 60,
        "1hour": 60 * 60,
        "2hours": 2 * 60 * 60,
        "1day": 24 * 60 * 60
    }
    
    if reping_time not in reminder_times:
        return
    
    seconds_before = reminder_times[reping_time]
    reminder_time = event_time - timedelta(seconds=seconds_before)
    
    # If the reminder time is in the past, don't schedule it
    if reminder_time < datetime.now(timezone.utc):
        await interaction.followup.send(
            f"The reminder time for {event_name} would be in the past, so no reminder will be sent.",
            ephemeral=True
        )
        return
    
    # Calculate seconds to wait
    seconds_to_wait = (reminder_time - datetime.now(timezone.utc)).total_seconds()
    
    # Log the scheduled reminder
    logger.info(f"Scheduling reminder for {event_name} in {seconds_to_wait} seconds")
    
    # Schedule the reminder
    bot.loop.create_task(
        send_reminder(
            message.channel.id, 
            event_name, 
            event_time, 
            activities, 
            importance,
            fc_name,
            doctrine_name,
            seconds_to_wait
        )
    )
    
    # Confirm to the user
    time_str = reminder_time.strftime("%Y-%m-%d %H:%M:%S UTC")
    await interaction.followup.send(
        f"Reminder scheduled for {time_str} ({reping_time} before the event)",
        ephemeral=True
    )

async def send_reminder(
    channel_id, 
    event_name, 
    event_time, 
    activities, 
    importance,
    fc_name,
    doctrine_name,
    delay_seconds
):
    """Send a reminder after the specified delay"""
    await asyncio.sleep(delay_seconds)
    
    try:
        channel = bot.get_channel(channel_id)
        if not channel:
            logger.error(f"Could not find channel {channel_id} for reminder")
            return
        
        # Create the reminder embed
        embed = discord.Embed(
            title=f"⏰ REMINDER: {event_name}",
            description=f"Event starts <t:{int(event_time.timestamp())}:R>",
            color=0xf1c40f  # Yellow for reminders
        )
        
        # Add field for local time
        embed.add_field(name="Local Time", value=f"<t:{int(event_time.timestamp())}:F>", inline=False)
        
        # Add FC and doctrine if provided
        if fc_name:
            embed.add_field(name="Fleet Commander", value=fc_name, inline=True)
        
        if doctrine_name:
            embed.add_field(name="Doctrine", value=doctrine_name, inline=True)
        
        if activities:
            embed.add_field(name="Activities", value=", ".join(activities), inline=True)
        
        embed.add_field(name="Importance", value=importance, inline=True)
        
        # Set color based on importance
        if importance == "High":
            embed.color = 0xe74c3c  # Red
            ping_text = "@here"
        elif importance == "Medium":
            embed.color = 0x3498db  # Blue
            ping_text = ""
        else:  # Low
            embed.color = 0x2ecc71  # Green
            ping_text = ""
        
        # Send the reminder
        if ping_text:
            await channel.send(ping_text, embed=embed)
        else:
            await channel.send(embed=embed)
            
    except Exception as e:
        logger.error(f"Error sending reminder: {str(e)}")

async def schedule_deletion(interaction, message, event_time):
    """Schedule message deletion 12 hours after the event"""
    deletion_time = event_time + timedelta(hours=12)
    now = datetime.now(timezone.utc)
    
    # Calculate seconds to wait
    wait_seconds = (deletion_time - now).total_seconds()
    
    # Schedule the deletion
    bot.loop.create_task(delete_timer_message(
        interaction.channel_id,
        message.id,
        wait_seconds,
        event_time
    ))
    
    logger.info(f"Scheduled deletion for timer message {message.id} in {wait_seconds} seconds (12 hours after event)")

async def delete_timer_message(channel_id, message_id, wait_seconds, event_time):
    """Delete a timer message after waiting the specified time"""
    await asyncio.sleep(wait_seconds)
    
    # Get the channel
    channel = bot.get_channel(channel_id)
    if not channel:
        logger.error(f"Could not find channel {channel_id} for timer deletion")
        return
    
    try:
        # Try to fetch and delete the message
        message = await channel.fetch_message(message_id)
        await message.delete()
        logger.info(f"Deleted timer message {message_id} (event was at {event_time})")
    except Exception as e:
        logger.error(f"Error deleting timer message {message_id}: {str(e)}")

@bot.tree.command(name="createtimer", description="Create a countdown timer (Admin only)")
async def create_timer(interaction: discord.Interaction):
    logger.info(f"Create timer command invoked by {interaction.user.name}")
    # Check if user has admin role
    admin_role = interaction.user.get_role(ADMIN_ROLE_ID)
    logger.info(f"Admin role check: {admin_role is not None}, Role ID: {ADMIN_ROLE_ID}")
    
    if not admin_role:
        logger.info(f"Permission denied for {interaction.user.name}")
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    
    # Show the timer creation modal directly
    logger.info(f"Showing timer modal to {interaction.user.name}")
    modal = TimerModal()
    await interaction.response.send_modal(modal)

@bot.event
async def on_ready():
    global _zkill_loop_running
    
    logger.info(f'{bot.user} has connected to Discord!')
    logger.info(f'Bot is in {len(bot.guilds)} servers')
    
    # Sync the commands with Discord
    try:
        logger.info("Attempting to sync commands with Discord...")
        synced = await bot.tree.sync()
        logger.info(f"Synced {len(synced)} command(s): {[cmd.name for cmd in synced]}")
    except Exception as e:
        logger.error(f"Failed to sync commands: {e}")
        logger.exception("Command sync error details:")
    
    # Debug channel access
    channel = bot.get_channel(KILLMAIL_CHANNEL_ID)
    if channel:
        logger.info(f'Found killmail channel: {channel.name}')
        permissions = channel.permissions_for(channel.guild.me)
        logger.info(f'Bot has send messages permission: {permissions.send_messages}')
        logger.info(f'Bot has embed links permission: {permissions.embed_links}')
    else:
        logger.error(f'Could not find killmail channel with ID: {KILLMAIL_CHANNEL_ID}')
    
    # Start tasks only if they're not already running
    if not update_bot_status.is_running():
        update_bot_status.start()
        logger.info("Started bot status update task")
    
    if not cache_save_task.is_running():
        cache_save_task.start()
        logger.info("Started cache save task")
    
    # Only start zkill loop if it's not already running
    if not _zkill_loop_running:
        _zkill_loop_running = True
        bot.loop.create_task(zkill_redisq_loop())
        logger.info("Started zkill RedisQ loop")
    else:
        logger.warning("zkill RedisQ loop already running, not starting another instance")

# For any other button classes that might be in the file
# For example, if there's a KillmailButtons class:

class KillmailButtons(discord.ui.View):
    def __init__(self, killmail_id):
        super().__init__(timeout=None)
        self.killmail_id = killmail_id
        
        # Add the Details button
        self.add_item(discord.ui.Button(
            label='Details',
            style=discord.ButtonStyle.primary,
            custom_id=f"details_{killmail_id}",
            row=0
        ))
        
        # Add the zKillboard link button
        self.add_item(discord.ui.Button(
            label='zKillboard',
            style=discord.ButtonStyle.link,
            url=f"https://zkillboard.com/kill/{killmail_id}/",
            row=0
        ))
    
    # Handle button interactions
    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        # Find which button was clicked by its custom_id
        if interaction.data.get("custom_id", "").startswith("details_"):
            await self.show_details(interaction)
        
        # For link buttons, Discord handles the navigation automatically
        return True
    
    async def show_details(self, interaction: discord.Interaction):
        # Implement details functionality here
        await interaction.response.send_message(f"Showing details for killmail {self.killmail_id}", ephemeral=True)



















