{% extends "base.html" %}

{% block content %}
<div class="complaint-detail">
    <article class="complaint-card">
        <header class="complaint-header">
            <h1 class="complaint-title">{{ complaint.title }}</h1>
            <div class="complaint-meta">
                <span class="complaint-author">Posted by {{ complaint.author }}</span>
                <span class="complaint-date">{{ complaint.created_at.strftime('%B %d, %Y %H:%M') }}</span>
                {% if complaint.complaint_type %}
                <span class="complaint-type">{{ complaint.complaint_type }}</span>
                {% endif %}
                {% if complaint.rage_level %}
                <span class="rage-level">Rage Level: {{ complaint.rage_level }}/10</span>
                {% endif %}
            </div>
        </header>

        <div class="complaint-content">
            {{ complaint.full_message }}
        </div>

        {% if complaint.zkill_link %}
        <div class="complaint-links">
            <a href="{{ complaint.zkill_link }}" target="_blank" rel="noopener noreferrer">
                View zKillboard
            </a>
        </div>
        {% endif %}

        <footer class="complaint-footer">
        </footer>
    </article>
</div>
{% endblock %}
