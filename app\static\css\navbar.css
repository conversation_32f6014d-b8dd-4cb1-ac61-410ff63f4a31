/* Styling for the navigation bar */
.navbar {
    background-color: #333;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    border-bottom: 3px solid #f7931a; /* Added orange highlight at the bottom */
    box-shadow: 0 2px 8px rgba(247, 147, 26, 0.3); /* Optional: adds a subtle glow effect */
}

/* Styling for links inside the navbar */
.navbar a {
    float: left;
    display: block;
    color: white;
    text-align: center;
    padding: 14px 16px;
    text-decoration: none;
}

/* Hover effect for navbar links */
.navbar a:hover {
    background-color: #ddd;
    color: black;
}

/* Styling for the active/current page link */
.navbar a.active {
    background-color: #4CAF50;
    color: white;
}

/* <PERSON><PERSON> Complaints badge */
.navbar a[href="/complaints"] {
    position: relative;
}

/* Removing the EVE bubble
.navbar a[href="/complaints"]:after {
    content: "EVE";
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: red;
    color: white;
    font-size: 10px;
    padding: 2px 5px;
    border-radius: 10px;
}
*/

/* Right-aligned navbar content */
.navbar-right {
    display: flex;
    align-items: center;
    margin-left: auto;
    gap: 10px;
}

/* Common button styling for donation buttons */
.paypal-button, .isk-button {
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    margin-right: 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-family: Arial, sans-serif;
    font-weight: bold;
    transition: background-color 0.3s;
}

/* PayPal button styling */
.paypal-button {
    background-color: #0070ba;
    color: white;
}

.paypal-button:hover {
    background-color: #005ea6;
}

.paypal-text {
    margin-right: 5px;
}

.paypal-logo {
    color: #fff;
    font-style: italic;
}

/* ISK Donation button styling */
.isk-button {
    background-color: #f7931a;
    color: white;
}

.isk-button:hover {
    background-color: #e07d0b;
}

.isk-text {
    margin-right: 5px;
}

.isk-character {
    font-style: italic;
}
