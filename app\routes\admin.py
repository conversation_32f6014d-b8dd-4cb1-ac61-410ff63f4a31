from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify
from functools import wraps
from app.config import Config
from app.models.models import Top<PERSON>, Visitor
from app.extensions import db
from datetime import datetime, timedelta
from sqlalchemy import func
import json
import os
from pathlib import Path

# Create the Blueprint with a consistent name
admin = Blueprint('admin', __name__)

def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'admin_logged_in' not in session:
            return redirect(url_for('admin.admin_login'))
        return f(*args, **kwargs)
    return decorated_function

@admin.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if username == Config.ADMIN_USERNAME and password == Config.ADMIN_PASSWORD:
            session['admin_logged_in'] = True
            return redirect(url_for('admin.admin_dashboard'))
        else:
            return render_template('admin_login.html', error='Invalid credentials')
    
    return render_template('admin_login.html')

@admin.route('/admin/logout')
def admin_logout():
    session.pop('admin_logged_in', None)
    return redirect(url_for('admin.admin_login'))

@admin.route('/admin/dashboard')
@admin_required
def admin_dashboard():
    # Define all form options
    form_options = {
        'complaint_types': [
            "Type 1", 
            "Type 2", 
            "Other (probably still CCP's fault)"
        ],
        'blame_options': [
            "CCP",
            "Goons",
            "TEST",
            "PL",
            "NC.",
            "My own incompetence (just kidding, it's CCP's fault)"
        ],
        'rage_levels': [
            "1 - Mild annoyance",
            "2 - Slightly irritated",
            "3 - Getting there",
            "4 - Pretty mad",
            "5 - Writing an angry forum post",
            "6 - Caps lock engaged",
            "7 - Biomassing alt",
            "8 - Unsubbing main",
            "9 - Writing to CCP CEO",
            "10 - Total meltdown"
        ],
        'quitting_options': [
            "Tomorrow",
            "Next week",
            "After the next patch",
            "Right after this war",
            "Just biomassed (see you next month)",
            "Never coming back (already resubbed)"
        ]
    }
    
    return render_template('admin.html', 
                         form_options=form_options,
                         title='Admin Dashboard')

@admin.route('/api/admin/complaints/pending')
@admin_required
def get_pending_complaints():
    try:
        complaints = Topic.query.filter_by(status='pending').order_by(Topic.created_at.desc()).all()
        complaints_list = []
        for c in complaints:
            complaints_list.append({
                'id': c.id,
                'title': c.title,
                'author': c.author,
                'date': c.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'preview': c.preview or (c.full_message[:100] + '...' if len(c.full_message) > 100 else c.full_message)
            })
        return jsonify({'status': 'success', 'complaints': complaints_list})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@admin.route('/api/admin/complaints/approved')
@admin_required
def get_approved_complaints():
    try:
        complaints = Topic.query.filter_by(status='approved').order_by(Topic.created_at.desc()).all()
        complaints_list = []
        for c in complaints:
            complaints_list.append({
                'id': c.id,
                'title': c.title,
                'author': c.author,
                'date': c.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'preview': c.preview or (c.full_message[:100] + '...' if len(c.full_message) > 100 else c.full_message)
            })
        return jsonify(complaints_list)
    except Exception as e:
        print(f"Error in get_approved_complaints: {str(e)}")
        return jsonify([])

@admin.route('/api/admin/complaints/denied')
@admin_required
def get_denied_complaints():
    try:
        complaints = Topic.query.filter_by(status='denied').order_by(Topic.created_at.desc()).all()
        complaints_list = []
        for c in complaints:
            complaints_list.append({
                'id': c.id,
                'title': c.title,
                'author': c.author,
                'date': c.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'preview': c.preview or (c.full_message[:100] + '...' if len(c.full_message) > 100 else c.full_message)
            })
        return jsonify(complaints_list)
    except Exception as e:
        print(f"Error in get_denied_complaints: {str(e)}")
        return jsonify([])

@admin.route('/api/admin/complaints/all')
@admin_required
def get_all_complaints():
    try:
        complaints = Topic.query.all()
        return jsonify([{
            'id': c.id,
            'title': c.title,
            'author': c.author,
            'full_message': c.full_message,
            'preview': c.preview,
            'complaint_type': c.complaint_type,
            'blame': c.blame,
            'rage_level': c.rage_level,
            'quitting': c.quitting,
            'zkill_link': c.zkill_link,
            'status': c.status,
            'created_at': c.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'view_count': c.view_count
        } for c in complaints])
    except Exception as e:
        print(f"Error in get_all_complaints: {str(e)}")
        return jsonify([])

@admin.route('/api/admin/complaints/<int:id>/approve', methods=['POST'])
@admin_required
def approve_complaint(id):
    try:
        complaint = Topic.query.get_or_404(id)
        complaint.status = 'approved'
        complaint.updated_at = datetime.utcnow()
        db.session.commit()
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@admin.route('/api/admin/complaints/<int:id>/deny', methods=['POST'])
@admin_required
def deny_complaint(id):
    try:
        complaint = Topic.query.get_or_404(id)
        complaint.status = 'denied'
        complaint.updated_at = datetime.utcnow()
        db.session.commit()
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@admin.route('/api/admin/complaints/<int:id>', methods=['GET', 'DELETE'])
@admin_required
def handle_complaint(id):
    try:
        complaint = Topic.query.get_or_404(id)
        
        if request.method == 'DELETE':
            db.session.delete(complaint)
            db.session.commit()
            return jsonify({'status': 'success'})
        
        # GET request - return complaint details
        return jsonify({
            'id': complaint.id,
            'title': complaint.title,
            'author': complaint.author,
            'date': complaint.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'status': complaint.status,
            'full_message': complaint.full_message,
            'preview': complaint.preview,
            'complaint_type': complaint.complaint_type,
            'blame': complaint.blame,
            'rage_level': complaint.rage_level,
            'quitting': complaint.quitting,
            'zkill_link': complaint.zkill_link if hasattr(complaint, 'zkill_link') else None
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@admin.route('/admin/complaint/<int:id>')
@admin_required
def view_complaint(id):
    complaint = Topic.query.get_or_404(id)
    return render_template('view_complaint.html',
                         complaint=complaint,
                         complaint_id=id,
                         status=complaint.status,
                         title='View Complaint')

@admin.route('/api/admin/stats')
@admin_required
def get_admin_stats():
    try:
        # Get existing complaint stats
        pending_count = Topic.query.filter_by(status='pending').count()
        approved_count = Topic.query.filter_by(status='approved').count()
        denied_count = Topic.query.filter_by(status='denied').count()
        
        # Get today's submissions
        today = datetime.utcnow().date()
        today_submissions = Topic.query.filter(
            func.date(Topic.created_at) == today
        ).count()
        
        # Get visitor stats
        total_visitors = Visitor.query.count()
        today_visitors = Visitor.query.filter(
            func.date(Visitor.last_visit) == today
        ).count()
        
        # Get active visitors (within last 15 minutes)
        fifteen_mins_ago = datetime.utcnow() - timedelta(minutes=15)
        active_visitors = Visitor.query.filter(
            Visitor.last_visit >= fifteen_mins_ago
        ).count()
        
        return jsonify({
            'status': 'success',
            'pending': pending_count,
            'approved': approved_count,
            'denied': denied_count,
            'today_submissions': today_submissions,
            'total_submissions': pending_count + approved_count + denied_count,
            'total_visitors': total_visitors,
            'today_visitors': today_visitors,
            'active_visitors': active_visitors
        })
    except Exception as e:
        print(f"Error in get_admin_stats: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)})

@admin.route('/api/admin/save-form-options', methods=['POST'])
@admin_required
def save_form_options():
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'message': 'No data received'
            }), 400
        
        # Ensure all required keys exist
        required_keys = ['complaintTypes', 'blameOptions', 'rageLevels', 'quittingOptions']
        for key in required_keys:
            if key not in data:
                return jsonify({
                    'status': 'error',
                    'message': f'Missing required key: {key}'
                }), 400

        # Convert the data format to match the public API format
        public_data = {
            'complaintTypes': [{'text': opt['text'], 'value': opt['value']} for opt in data['complaintTypes']],
            'blameOptions': [{'text': opt['text'], 'value': opt['value']} for opt in data['blameOptions']],
            'rageLevels': [{'text': opt['text'], 'value': opt['value']} for opt in data['rageLevels']],
            'quittingOptions': [{'text': opt['text'], 'value': opt['value']} for opt in data['quittingOptions']]
        }

        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'form_options.json')
        
        # Save to file
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(public_data, f, indent=4)
        
        return jsonify({
            'status': 'success',
            'message': 'Form options saved successfully'
        })
    except Exception as e:
        print(f"Error saving form options: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@admin.route('/api/admin/form-options', methods=['GET'])
@admin_required
def get_form_options():
    try:
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'form_options.json')
        print(f"Loading from path: {config_path}")
        
        # If the file doesn't exist, return default options
        if not os.path.exists(config_path):
            print("Config file does not exist, returning default options")
            return jsonify({
                'status': 'success',
                'data': {
                    'complaintTypes': [],
                    'blameOptions': [],
                    'rageLevels': [],
                    'quittingOptions': []
                }
            })
        
        # Read and return the saved options
        with open(config_path, 'r', encoding='utf-8') as f:
            options = json.load(f)
            print(f"Loaded options: {options}")
            
        return jsonify({
            'status': 'success',
            'data': options
        })
    except Exception as e:
        import traceback
        print(f"Error loading form options: {str(e)}")
        print(traceback.format_exc())  # Print full stack trace
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@admin.route('/api/admin/form-options/reset', methods=['POST'])
@admin_required
def reset_form_options():
    try:
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'form_options.json')
        
        # Delete the config file if it exists
        if os.path.exists(config_path):
            os.remove(config_path)
        
        return jsonify({
            'status': 'success',
            'message': 'Form options reset to defaults'
        })
    except Exception as e:
        print(f"Error resetting form options: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

# Add this new route for public access to form options
@admin.route('/api/form-options', methods=['GET'])
def get_public_form_options():
    try:
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'form_options.json')
        
        # If the file doesn't exist, return default options
        if not os.path.exists(config_path):
            return jsonify({
                'status': 'success',
                'data': {
                    'complaintTypes': [
                        {'text': 'Game Balance Issue', 'value': 'balance'},
                        {'text': 'UI/UX Complaint', 'value': 'ui_ux'},
                        {'text': 'Server Performance', 'value': 'server'},
                        {'text': 'PvP Mechanics', 'value': 'pvp'},
                        {'text': 'Economic Changes', 'value': 'economy'},
                        {'text': 'Other (probably still CCP\'s fault)', 'value': 'other'}
                    ],
                    'blameOptions': [
                        {'text': 'CCP Games (obviously)', 'value': 'ccp'},
                        {'text': 'My Corp Leadership', 'value': 'corp_leaders'},
                        {'text': 'Those Darn Null Sec Blocs', 'value': 'null_blocs'},
                        {'text': 'The Market Manipulators', 'value': 'market_traders'},
                        {'text': 'My Own Poor Life Choices', 'value': 'self'},
                        {'text': 'RNG Jesus', 'value': 'rng'}
                    ],
                    'rageLevels': [
                        {'text': '1 - Mild Disappointment', 'value': '1'},
                        {'text': '3 - Angry Mumbling', 'value': '3'},
                        {'text': '5 - Writing an Angry Forum Post', 'value': '5'},
                        {'text': '7 - Keyboard Smashing', 'value': '7'},
                        {'text': '9 - Uninstalling the Game', 'value': '9'},
                        {'text': '10 - Creating a Reddit Megathread', 'value': '10'}
                    ],
                    'quittingOptions': [
                        {'text': 'Just Until Tomorrow', 'value': 'tomorrow'},
                        {'text': 'One Week Tops', 'value': 'week'},
                        {'text': 'Until the Next Patch', 'value': 'patch'},
                        {'text': 'Until My Corp Needs Me', 'value': 'corp_needs'},
                        {'text': 'Until I See a Cool Ship Screenshot', 'value': 'screenshot'},
                        {'text': 'This Time I Mean It (Sure...)', 'value': 'forever'}
                    ]
                }
            })
        
        # Read and return the saved options
        with open(config_path, 'r', encoding='utf-8') as f:
            options = json.load(f)
            
        return jsonify({
            'status': 'success',
            'data': options
        })
    except Exception as e:
        print(f"Error loading form options: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@admin.route('/api/admin/save-options', methods=['POST'])
@admin_required
def save_options():
    try:
        data = request.get_json()
        option_type = data.get('type')
        options = data.get('options', [])
        
        # Log the received data (for debugging)
        print(f"Saving options for {option_type}: {options}")
        
        # Here you would typically save these to your database or config file
        # For now, we'll just return success
        return jsonify({'status': 'success', 'message': 'Options saved successfully'})
    except Exception as e:
        print(f"Error saving options: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500
