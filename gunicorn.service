[Unit]
Description=Gunicorn instance to serve Sky Daddy Ranch
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/var/www/skydaddyranch
Environment="PATH=/var/www/skydaddyranch/venv/bin"
Environment="DISCORD_TOKEN=MTMzNjk2NzIzMjEzOTQzMjAwNw.GABHVG.ak2F6C61AToco65__k8LptPuaoRoEGofFXqZFg"
Environment="COMPLAINT_CHANNEL_ID=1334400480075583519"
Environment="ADMIN_ROLE_ID=1334400479735582748"
ExecStart=/var/www/skydaddyranch/venv/bin/gunicorn --workers 3 --bind unix:/var/www/skydaddyranch/sky_daddy_ranch.sock -m 007 wsgi:app

[Install]
WantedBy=multi-user.target
