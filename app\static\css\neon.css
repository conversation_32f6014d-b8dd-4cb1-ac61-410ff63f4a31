/* Neon Theme - Base colors and effects */
:root {
  --neon-blue: #00f3ff;
  --neon-orange: #ff7b00;
  --neon-white: #ffffff;
  --dark-bg: #1a1a1a; /* Changed from #0a0a1a to a dark grey */
  --glow-blue: 0 0 10px var(--neon-blue), 0 0 20px var(--neon-blue);
  --glow-orange: 0 0 10px var(--neon-orange), 0 0 20px var(--neon-orange);
  --glow-white: 0 0 10px var(--neon-white), 0 0 20px var(--neon-white);
}

/* Base styling */
body {
  background-color: var(--dark-bg);
  color: var(--neon-white);
  font-family: 'Orbitron', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.container {
  width: 85%;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid var(--neon-blue);
  box-shadow: var(--glow-blue);
  border-radius: 8px;
  margin-top: 20px;
}

.content {
  padding: 20px;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
  color: var(--neon-orange);
  text-shadow: var(--glow-orange);
  letter-spacing: 2px;
}

/* Links */
a {
  color: var(--neon-blue);
  text-decoration: none;
  transition: all 0.3s ease;
  text-shadow: var(--glow-blue);
}

a:hover {
  color: var(--neon-orange);
  text-shadow: var(--glow-orange);
}

/* Buttons */
.btn, button, input[type="submit"], input[type="button"] {
  background-color: transparent;
  color: var(--neon-blue);
  border: 1px solid var(--neon-blue);
  box-shadow: var(--glow-blue);
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.btn:hover, button:hover, input[type="submit"]:hover, input[type="button"]:hover {
  background-color: var(--neon-blue);
  color: var(--dark-bg);
  box-shadow: var(--glow-blue);
}

/* Primary button - orange variant */
.primary-btn {
  color: var(--neon-orange);
  border: 1px solid var(--neon-orange);
  box-shadow: var(--glow-orange);
}

.primary-btn:hover {
  background-color: var(--neon-orange);
  color: var(--dark-bg);
  box-shadow: var(--glow-orange);
}

/* Form elements */
input, textarea, select {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--neon-blue);
  color: var(--neon-white);
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 0 5px var(--neon-blue);
  transition: all 0.3s ease;
}

input:focus, textarea:focus, select:focus {
  border-color: var(--neon-orange);
  box-shadow: 0 0 10px var(--neon-orange);
  outline: none;
}

/* Navbar neon styling */
.navbar {
  background-color: rgba(10, 10, 26, 0.8);
  border-bottom: 1px solid var(--neon-blue);
  box-shadow: var(--glow-blue);
}

.navbar a {
  color: var(--neon-white);
  text-shadow: var(--glow-white);
}

.navbar a:hover {
  background-color: var(--neon-blue);
  color: var(--dark-bg);
  text-shadow: none;
}

.navbar a.active {
  background-color: var(--neon-orange);
  color: var(--dark-bg);
  text-shadow: none;
  box-shadow: var(--glow-orange);
}

/* Donation buttons */
.isk-button {
  background-color: transparent;
  color: var(--neon-orange);
  border: 1px solid var(--neon-orange);
  box-shadow: var(--glow-orange);
}

.isk-button:hover {
  background-color: var(--neon-orange);
  color: var(--dark-bg);
}

.paypal-button {
  background-color: transparent;
  color: var(--neon-blue);
  border: 1px solid var(--neon-blue);
  box-shadow: var(--glow-blue);
}

.paypal-button:hover {
  background-color: var(--neon-blue);
  color: var(--dark-bg);
}

/* Modal styling */
.modal-content {
  background-color: var(--dark-bg);
  border: 1px solid var(--neon-blue);
  box-shadow: var(--glow-blue);
}

.character-name {
  color: var(--neon-orange);
  text-shadow: var(--glow-orange);
  border: 1px solid var(--neon-orange);
  box-shadow: var(--glow-orange);
}

/* Animation for neon flicker effect */
@keyframes neonFlicker {
  0%, 19%, 21%, 23%, 25%, 54%, 56%, 100% {
    text-shadow: var(--glow-blue);
    box-shadow: var(--glow-blue);
  }
  20%, 24%, 55% {
    text-shadow: none;
    box-shadow: none;
  }
}

/* Apply flicker animation to certain elements */
.hero-section h1, .section-title {
  animation: neonFlicker 5s infinite alternate;
}

/* Forum styling - neon version */
.forum-topics {
  margin-bottom: 30px;
}

.topic-card {
  border: 1px solid var(--neon-blue);
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: rgba(0, 243, 255, 0.05);
  box-shadow: 0 0 5px var(--neon-blue);
}

.topic-card h3 {
  margin-top: 0;
  color: var(--neon-orange);
  text-shadow: var(--glow-orange);
}

.topic-meta {
  color: var(--neon-white);
  font-size: 0.9em;
  margin-bottom: 10px;
}

/* Message content formatting */
.message-content {
  white-space: pre-wrap;
  font-family: 'Orbitron', sans-serif;
  background-color: rgba(255, 255, 255, 0.05);
  padding: 15px;
  border-radius: 4px;
  border-left: 3px solid var(--neon-orange);
  overflow-x: auto;
  color: var(--neon-white);
}

/* Complaint form styling - neon version */
.complaint-form {
  max-width: 800px;
  margin: 0 auto;
}

.complaint-container {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  margin: 20px 0;
}

.hr-department {
  flex: 1;
  min-width: 300px;
  max-width: 400px;
  background-color: rgba(10, 10, 26, 0.8);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 0 10px var(--neon-orange);
  border-left: 4px solid var(--neon-orange);
}

.hr-department h2 {
  color: var(--neon-orange);
  border-bottom: 2px solid var(--neon-orange);
  padding-bottom: 10px;
  text-shadow: var(--glow-orange);
}

.hr-disclaimer {
  font-size: 0.8em;
  color: var(--neon-white);
  margin-top: 20px;
  border-top: 1px dashed var(--neon-blue);
  padding-top: 10px;
}

/* Error message styling - neon version */
.error {
  color: var(--neon-orange);
  background-color: rgba(255, 123, 0, 0.1);
  border-left: 3px solid var(--neon-orange);
  padding: 10px 15px;
  border-radius: 0 4px 4px 0;
  margin-bottom: 15px;
  box-shadow: 0 0 5px var(--neon-orange);
}

/* Loading indicator - neon version */
.loading {
  text-align: center;
  color: var(--neon-blue);
  font-style: italic;
  padding: 10px;
  text-shadow: var(--glow-blue);
}

/* Home page specific neon styling */
.modern-home-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.hero-section {
  background: linear-gradient(135deg, #0a0a1a 0%, #000000 100%);
  border: 1px solid var(--neon-blue);
  box-shadow: var(--glow-blue);
  padding: 40px 20px;
  margin-bottom: 30px;
}

.hero-logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.hero-logo {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  box-shadow: 0 0 15px var(--neon-blue);
  transition: transform 0.5s ease;
}

.hero-section h1 {
  font-size: 2.8rem;
  margin-bottom: 15px;
}

.hero-tagline {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.features-section {
  background-color: rgba(10, 10, 26, 0.5);
  border-radius: 10px;
  border: 1px solid var(--neon-blue);
  box-shadow: var(--glow-blue);
  padding: 30px 20px;
  margin-bottom: 30px;
}

.feature-box {
  background-color: rgba(10, 10, 26, 0.8);
  border: 1px solid var(--neon-blue);
  box-shadow: 0 0 5px var(--neon-blue);
  padding: 20px;
}

.feature-icon img {
  width: 60px;
  height: 60px;
}

.quote-section {
  background-color: rgba(10, 10, 26, 0.5);
  border: 1px solid var(--neon-blue);
  box-shadow: var(--glow-blue);
  padding: 40px 20px;
  margin-bottom: 30px;
}

.quote-section blockquote {
  font-size: 1.5rem;
  color: var(--neon-white);
}

.about-eve-section {
  border: 1px solid var(--neon-blue);
  box-shadow: var(--glow-blue);
  margin-bottom: 30px;
}

.about-eve-text {
  padding: 30px;
}

.about-eve-image img {
  max-height: 250px;
}

section {
  padding: 40px 20px;
  margin-bottom: 30px;
}

.section-title {
  font-size: 1.8rem;
  margin-bottom: 30px;
}
