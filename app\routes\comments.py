from flask import Blueprint, jsonify, request, session
from app.models.models import Comment
from app.extensions import db
from datetime import datetime

comments = Blueprint('comments', __name__)

@comments.route('/api/complaints/<int:complaint_id>/comments', methods=['GET'])
def get_comments(complaint_id):
    comments = Comment.query.filter_by(topic_id=complaint_id).order_by(Comment.timestamp.desc()).all()
    
    return jsonify([{
        'id': comment.id,
        'author': comment.author,
        'text': comment.text,
        'timestamp': comment.timestamp.strftime('%B %d, %Y %H:%M')
    } for comment in comments])

@comments.route('/api/complaints/<int:complaint_id>/comments', methods=['POST'])
def add_comment(complaint_id):
    try:
        if not request.is_json:
            return jsonify({'status': 'error', 'message': 'Invalid content type, expected JSON'}), 400

        data = request.get_json()
        text = data.get('text')
        
        if not text:
            return jsonify({'status': 'error', 'message': 'Comment text is required'}), 400

        new_comment = Comment(
            topic_id=complaint_id,
            author=session.get('username', 'Anonymous'),
            text=text,
            timestamp=datetime.utcnow()
        )
        
        db.session.add(new_comment)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'comment': {
                'id': new_comment.id,
                'author': new_comment.author,
                'text': new_comment.text,
                'timestamp': new_comment.timestamp.strftime('%B %d, %Y %H:%M')
            }
        })

    except Exception as e:
        print(f"Error in add_comment: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


