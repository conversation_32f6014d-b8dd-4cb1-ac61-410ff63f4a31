{% extends "base.html" %}  <!-- Tells Jinja2 to use base.html as the parent template -->

{% block content %}  <!-- Defines the content that will replace the block in the parent template -->
    <div class="header-center">
        <h1>Sky Daddy Ranch Complaint Department</h1>
        <p>Lost your ship? Got ganked in high-sec? CCP nerfed your favorite strategy? Let us know!</p>
    </div>
    
    <div class="complaint-container">
        <!-- New HR Department section -->
        <div class="hr-department">
            <h2>HR Department Mission Statement</h2>
            <div class="hr-motto">
                <blockquote>"We pretend to care so you can pretend we're listening."</blockquote>
            </div>
            
            <div class="hr-message">
                <h3>Our Commitment to You</h3>
                <p>Here at Sky Daddy Ranch, we understand that New Eden can be a harsh and unforgiving place. Our dedicated team of former rage-quitters is standing by to carefully file your complaint in our state-of-the-art circular storage system.</p>
                
                <h3>The SDR Guarantee</h3>
                <ul class="hr-guarantee-list">
                    <li><span class="guarantee-icon">🔍</span> We thoroughly investigate all complaints (by skimming them while on lunch break)</li>
                    <li><span class="guarantee-icon">⏱️</span> We respond to all inquiries within 2-3 business millennia</li>
                    <li><span class="guarantee-icon">🤝</span> We promise to take your feedback seriously (while laughing hysterically)</li>
                    <li><span class="guarantee-icon">💰</span> We offer the same reimbursement policy as CCP: absolutely nothing</li>
                </ul>
                
                <div class="hr-testimonial">
                    <p>"After losing my third Titan this month, Sky Daddy Ranch's complaint form was the only thing that kept me from biomassing my character. Their complete lack of actual assistance was exactly what I needed!"</p>
                    <cite>— xXElite_PvPerXx, Professional Victim</cite>
                </div>
                
                <div class="hr-disclaimer">
                    <p><small>* Sky Daddy Ranch is not affiliated with CCP Games. Submitting a complaint here has the same effect as screaming into the void, but with more memes.</small></p>
                </div>
            </div>
        </div>
        
        <!-- Existing complaint form -->
        <form class="complaint-form" method="POST">
            <!-- Add a hidden input for the form token -->
            <input type="hidden" name="form_token" value="{{ form_token }}">
            
            <div class="form-group">
                <label for="name">Your Pilot Name:</label>
                <input type="text" id="name" name="name" placeholder="xXx_Elite_PvPer_xXx" required>
            </div>
            
            <div class="form-group">
                <label for="complaint_type">Type of Complaint:</label>
                <select id="complaint_type" name="complaint_type" required>
                    <option value="" disabled selected>Select your misfortune</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="blame">Who's to Blame:</label>
                <select id="blame" name="blame" required>
                    <option value="" disabled selected>Select the guilty party</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="title">Complaint Title:</label>
                <input type="text" id="title" name="title" placeholder="CCP RUINED EVE AGAIN!!1!" required>
            </div>
            
            <div class="form-group zkill-container">
                <label for="zkill_link">zKillboard Link (optional):</label>
                <div class="zkill-input-container">
                    <input type="url" id="zkill_link" name="zkill_link" placeholder="https://zkillboard.com/kill/12345678/">
                    <span id="zkill_verification" class="verification-icon"></span>
                </div>
                <small class="form-text text-muted">Paste a zKillboard link to prove your loss</small>
            </div>
            
            <div class="form-group">
                <label for="message">Your Complaint:</label>
                <textarea id="message" name="message" rows="5" placeholder="Tell us how the game is dying and how you're definitely quitting this time..." required></textarea>
            </div>
            
            <div class="form-group">
                <label for="rage_level">Rage Level (1-10):</label>
                <select id="rage_level" name="rage_level" required>
                    <option value="" disabled selected>Select your rage level</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="quitting">How long until you return after "quitting forever"?</label>
                <select id="quitting" name="quitting" required>
                    <option value="" disabled selected>Select your return timeline</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="reimbursement">Reimbursement Options:</label>
                <select id="reimbursement" name="reimbursement" required>
                    <option value="no" selected>No</option>
                    <option value="isk" disabled>Full ISK reimbursement (greyed out for your disappointment)</option>
                    <option value="plex" disabled>PLEX compensation (nice try)</option>
                    <option value="ship" disabled>Replacement ship (in your dreams)</option>
                    <option value="tears" disabled>Bottle of your own tears (we already have plenty)</option>
                    <option value="therapy" disabled>Free therapy session (you need it)</option>
                </select>
                
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn submit-btn">
                    <i class="fas fa-paper-plane"></i> Submit Complaint
                </button>
            </div>
        </form>
    </div>

    <script>
        // Function to populate form dropdowns
        function populateFormOptions() {
            fetch('/api/form-options')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success' && data.data) {
                        const options = data.data;
                        
                        // Populate complaint types
                        const complaintTypeSelect = document.getElementById('complaint_type');
                        complaintTypeSelect.innerHTML = '<option value="">Select a complaint type</option>';
                        options.complaintTypes.forEach(opt => {
                            const option = document.createElement('option');
                            option.value = opt.value;
                            option.textContent = opt.text;
                            complaintTypeSelect.appendChild(option);
                        });

                        // Populate blame options
                        const blameSelect = document.getElementById('blame');
                        blameSelect.innerHTML = '<option value="">Who to blame?</option>';
                        options.blameOptions.forEach(opt => {
                            const option = document.createElement('option');
                            option.value = opt.value;
                            option.textContent = opt.text;
                            blameSelect.appendChild(option);
                        });

                        // Populate rage levels
                        const rageSelect = document.getElementById('rage_level');
                        options.rageLevels.forEach(option => {
                            const opt = document.createElement('option');
                            opt.value = option.value;
                            opt.textContent = option.text;
                            rageSelect.appendChild(opt);
                        });

                        // Populate quitting options
                        const quittingSelect = document.getElementById('quitting');
                        options.quittingOptions.forEach(option => {
                            const opt = document.createElement('option');
                            opt.value = option.value;
                            opt.textContent = option.text;
                            quittingSelect.appendChild(opt);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading form options:', error);
                });
        }

        // Call the function when the page loads
        document.addEventListener('DOMContentLoaded', populateFormOptions);

        // Add event listener to verify zkill link
        document.getElementById('zkill_link').addEventListener('input', verifyZkillLink);
        
        function verifyZkillLink() {
            const zkillInput = document.getElementById('zkill_link');
            const verificationIcon = document.getElementById('zkill_verification');
            const zkillUrl = zkillInput.value.trim();
            
            // Reset verification icon
            verificationIcon.className = 'verification-icon';
            
            if (!zkillUrl) {
                return;
            }
            
            // Simple regex to validate zkillboard URL format
            const zkillRegex = /^https?:\/\/(www\.)?zkillboard\.com\/kill\/\d+\/?$/i;
            
            if (zkillRegex.test(zkillUrl)) {
                // Valid zkill link format
                verificationIcon.className = 'verification-icon verified';
                verificationIcon.title = 'Valid zKillboard link';
            } else {
                // Invalid zkill link format
                verificationIcon.className = 'verification-icon invalid';
                verificationIcon.title = 'Invalid zKillboard link format';
            }
        }
        
        // Add this notification function specifically for the complaints form
        function showComplaintPopup(message, type = 'success') {
            const popup = document.createElement('div');
            popup.className = `complaint-popup ${type}`;
            popup.innerHTML = `
                <div class="popup-content">
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
                    <p>${message}</p>
                </div>
            `;
            document.body.appendChild(popup);
            
            // Add animation class after a brief delay
            setTimeout(() => popup.classList.add('show'), 10);
            
            // Remove popup after animation
            setTimeout(() => {
                popup.classList.remove('show');
                setTimeout(() => popup.remove(), 300);
            }, 3000);
        }

        document.querySelector('.complaint-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitButton = this.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Filing your complaint into the void...';
            
            const loadingMessages = [
                "Channeling your rage into productive memes...",
                "Converting tears into content...",
                "Dispatching space therapists...",
                "Measuring salt levels...",
                "Calculating probability of actual rage quit...",
                "Preparing standard 'git gud' response...",
                "Redirecting complaint to /dev/null...",
                "Searching for spare ISK in couch cushions...",
                "Consulting with Bob from wormhole space...",
                "Checking if CCP actually cares (spoiler: no)..."
            ];
            
            let messageIndex = 0;
            const messageInterval = setInterval(() => {
                submitButton.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${loadingMessages[messageIndex]}`;
                messageIndex = (messageIndex + 1) % loadingMessages.length;
            }, 2000);
            
            const formData = new FormData(this);
            
            fetch('/post_to_discord', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                clearInterval(messageInterval);
                if (data.status === 'success') {
                    submitButton.innerHTML = '<i class="fas fa-check"></i> Complaint filed successfully!';
                    this.reset();
                    
                    const randomSuccessMessages = [
                        "Your salt has been harvested successfully!",
                        "Complaint filed! Your tears will make a fine addition to our collection.",
                        "Thank you for your feedback! It will be carefully ignored.",
                        "Complaint received! Have you considered mining in high-sec instead?",
                        "Success! Your complaint has joined countless others in the void.",
                        "Complaint processed! Our team of trained monkeys will review it shortly.",
                        "Your rage has been documented for future generations to study.",
                        "Thank you for your salt donation to the Eve Drama Museum!",
                        "Complaint accepted! Would you like to upgrade to our Premium Salt Package?",
                        "Successfully filed under 'Things CCP Won't Read'!"
                    ];
                    const randomMessage = randomSuccessMessages[Math.floor(Math.random() * randomSuccessMessages.length)];
                    showComplaintPopup(randomMessage, 'success');
                    
                    // Generate new form token
                    fetch('/complaints')
                        .then(response => response.text())
                        .then(html => {
                            const parser = new DOMParser();
                            const doc = parser.parseFromString(html, 'text/html');
                            const newToken = doc.querySelector('input[name="form_token"]').value;
                            document.querySelector('input[name="form_token"]').value = newToken;
                        });
                        
                    // Reset form options
                    loadFormOptions();
                } else if (data.status === 'duplicate') {
                    submitButton.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Nice try, but we already have this salt';
                    showComplaintPopup("This complaint appears to be a duplicate. Please try being angry about something else.", 'error');
                } else {
                    throw new Error(data.message || 'Failed to submit complaint');
                }
            })
            .catch(error => {
                clearInterval(messageInterval);
                console.error('Error:', error);
                submitButton.innerHTML = '<i class="fas fa-times"></i> Even we couldn\'t handle this level of salt';
                showComplaintPopup('Error submitting complaint. Have you tried turning your ship off and on again?', 'error');
            })
            .finally(() => {
                setTimeout(() => {
                    submitButton.disabled = false;
                    submitButton.innerHTML = '<i class="fas fa-paper-plane"></i> Submit Another Complaint';
                }, 3000);
            });
        });
    </script>
{% endblock %}  <!-- Ends the content block -->

<style>
    /* Modal styles */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        overflow: auto;
    }
    
    .modal-content {
        background-color: #1a1a2e;
        margin: 15% auto;
        padding: 20px;
        border-radius: 8px;
        width: 80%;
        max-width: 600px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
        animation: modalopen 0.4s;
    }
    
    @keyframes modalopen {
        from {opacity: 0; transform: translateY(-60px);}
        to {opacity: 1; transform: translateY(0);}
    }
    
    .close-button {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
    }
    
    .close-button:hover,
    .close-button:focus {
        color: #fff;
        text-decoration: none;
    }
    
    .modal h2 {
        color: #d9534f;
        border-bottom: 2px solid #d9534f;
        padding-bottom: 10px;
        margin-top: 0;
    }
    
    .message-box {
        padding: 15px;
        margin: 20px 0;
        border-radius: 5px;
        font-size: 1.1em;
    }
    
    .message-box.error {
        background-color: rgba(217, 83, 79, 0.1);
        border-left: 3px solid #d9534f;
        color: #f8f8f8;
    }
    
    .modal-buttons {
        display: flex;
        gap: 15px;
        margin-top: 20px;
    }
    
    .modal-buttons .btn {
        flex: 1;
        text-align: center;
    }
    
    .modal-buttons .btn.secondary {
        background-color: #5bc0de;
    }
    
    .modal-buttons .btn.secondary:hover {
        background-color: #46b8da;
    }

    .user-popup {
        display: none;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        z-index: 1000;
        max-width: 400px;
        text-align: center;
    }

    .user-popup-content {
        margin: 10px 0;
    }

    .complaint-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px;
        border-radius: 5px;
        z-index: 1000;
        animation: slideIn 0.3s ease-out;
        color: white;
    }

    .complaint-notification.success {
        background-color: #4CAF50;
    }

    .complaint-notification.warning {
        background-color: #ff9800;
    }

    .complaint-notification.error {
        background-color: #f44336;
    }

    .notification-content {
        padding: 10px;
    }

    .notification-content p {
        margin: 0;
    }

    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    .complaint-popup {
        position: fixed;
        top: 20px;
        right: 20px;
        max-width: 400px;
        padding: 15px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease-in-out;
    }

    .complaint-popup.show {
        opacity: 1;
        transform: translateX(0);
    }

    .complaint-popup .popup-content {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .complaint-popup i {
        font-size: 24px;
    }

    .complaint-popup.success {
        border-left: 4px solid #4CAF50;
    }

    .complaint-popup.success i {
        color: #4CAF50;
    }

    .complaint-popup.error {
        border-left: 4px solid #f44336;
    }

    .complaint-popup.error i {
        color: #f44336;
    }

    .complaint-popup p {
        margin: 0;
        font-size: 14px;
        color: #333;
    }
</style>
