/* Basic styling for the entire page */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    line-height: 1.6;
    background-color: #333333; /* Dark grey background */
    color: #f0f0f0; /* Light text color for better contrast */
}

/* Container to center content and set width */
.container {
    width: 80%;
    margin: 0 auto;
    padding: 20px;
}

/* Styling for the main content area */
.content {
    padding: 20px;
    background-color: #444444; /* Slightly lighter grey for content area */
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Common button styling */
.btn {
    display: inline-block;
    background-color: #4CAF50;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
}

.btn:hover {
    background-color: #45a049;
}

/* Error message styling */
.error {
    color: #ff6b6b;
    background-color: rgba(217, 83, 79, 0.2);
    border-left: 3px solid #d9534f;
    padding: 10px 15px;
    border-radius: 0 4px 4px 0;
    margin-bottom: 15px;
}

.loading {
    text-align: center;
    color: #aaa;
    font-style: italic;
    padding: 10px;
}
