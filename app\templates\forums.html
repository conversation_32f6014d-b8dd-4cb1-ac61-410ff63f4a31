{% extends "base.html" %}

{% block content %}
{# Add this at the top of the template to load the form options #}
{% set form_options = load_form_options() %}

<div class="forums-container">
    <div class="forums-header">
        <h1>Sky Daddy Ranch Forums</h1>
    </div>

    <div class="forums-content">
        <div class="forums-controls">
            <div class="search-sort">
                <input type="text" id="searchInput" placeholder="Search complaints...">
                <select id="sortSelect">
                    <option value="date">Sort by Date</option>
                    <option value="rage">Sort by Rage Level</option>
                </select>
            </div>
        </div>

        <div class="complaints-list">
            {% if topics %}
                {% for topic in topics %}
                <div class="complaint-row">
                    <div class="complaint-main">
                        <div class="complaint-header">
                            <div class="title-status">
                                <h3>{{ topic.title }}</h3>
                            </div>
                            <div class="complaint-meta">
                                <span class="author">
                                    <i class="fas fa-user"></i> {{ topic.author }}
                                </span>
                                <span class="date">
                                    <i class="fas fa-calendar"></i> {{ topic.created_at.strftime('%Y-%m-%d %H:%M') }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="complaint-content">
                            {{ topic.full_message }}
                        </div>
                    </div>

                    <div class="complaint-details">
                        <div class="detail-item">
                            <label>Type:</label>
                            <span>{{ get_option_text(form_options.complaintTypes, topic.complaint_type) }}</span>
                        </div>
                        <div class="detail-item">
                            <label>Blame:</label>
                            <span>{{ get_option_text(form_options.blameOptions, topic.blame) }}</span>
                        </div>
                        <div class="detail-item">
                            <label>Rage Level:</label>
                            <span class="rage-level">{{ get_option_text(form_options.rageLevels, topic.rage_level) }}</span>
                        </div>
                        <div class="detail-item">
                            <label>Quitting For:</label>
                            <span class="quitting-status">{{ get_option_text(form_options.quittingOptions, topic.quitting) }}</span>
                        </div>
                        {% if topic.zkill_link %}
                        <div class="detail-item">
                            <label>Zkill:</label>
                            <a href="{{ topic.zkill_link }}" target="_blank" class="zkill-link">View Killmail</a>
                        </div>
                        {% endif %}
                    </div>

                    <div class="complaint-footer">
                        <div class="complaint-votes">
                            <button class="vote-btn upvote {% if topic.user_vote == 'up' %}voted{% endif %}" 
                                    onclick="voteComplaint({{ topic.id }}, 'up', this)">
                                <i class="fas fa-thumbs-up"></i>
                            </button>
                            <span class="vote-count">{{ topic.upvotes - topic.downvotes }}</span>
                            <button class="vote-btn downvote {% if topic.user_vote == 'down' %}voted{% endif %}"
                                    onclick="voteComplaint({{ topic.id }}, 'down', this)">
                                <i class="fas fa-thumbs-down"></i>
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <p class="empty-message">No complaints found.</p>
            {% endif %}
        </div>
    </div>
</div>
<script>
function voteComplaint(complaintId, voteType, button) {
    fetch(`/api/complaints/${complaintId}/vote`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ vote_type: voteType })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            const voteContainer = button.parentElement;
            const voteCount = voteContainer.querySelector('.vote-count');
            const upvoteBtn = voteContainer.querySelector('.upvote');
            const downvoteBtn = voteContainer.querySelector('.downvote');
            
            // Reset both buttons
            upvoteBtn.classList.remove('voted');
            downvoteBtn.classList.remove('voted');
            
            if (data.action === 'added') {
                button.classList.add('voted');
            } else if (data.action === 'changed') {
                button.classList.add('voted');
            }
            
            // Refresh the page to update vote counts
            // You could alternatively update just the count via API
            location.reload();
        }
    })
    .catch(error => console.error('Error:', error));
}
</script>
{% endblock %}

