/* Comments styling */
.comments-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #333;
}

.comment {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.comment-text {
    color: #f0f0f0;
    line-height: 1.4;
}

.comment-form {
    margin-top: 1rem;
}

.comment-input {
    width: 100%;
    min-height: 100px;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 4px;
    color: #f0f0f0;
    resize: vertical;
}

.no-comments {
    font-style: italic;
    color: #aaa;
}

/* Comment vote buttons */
.comment-vote-buttons {
    justify-content: flex-end;
    margin-top: 5px;
}

.recent-comment {
    margin-top: 10px;
    padding: 10px;
    background-color: #333333; /* Darker background */
    border-radius: 4px;
    border-left: 3px solid #555;
}

.recent-comment h4 {
    margin-top: 0;
    margin-bottom: 5px;
    font-size: 0.9em;
    color: #aaa; /* Lighter color for better contrast */
}

.recent-comment .comment-text {
    margin: 0;
    font-style: italic;
    color: #f0f0f0; /* Light text color */
}

.recent-comment .comment-meta {
    margin-top: 5px;
    font-size: 0.8em;
    color: #aaa; /* Lighter color for better contrast */
}
