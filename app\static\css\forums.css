.forums-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.forums-header {
    text-align: center;
    margin-bottom: 40px;
}

.forums-header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    color: var(--neon-orange);
    text-shadow: var(--glow-orange);
}

.forums-header p {
    color: var(--neon-white);
    font-size: 1.2em;
}

.forums-controls {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.search-sort {
    display: flex;
    gap: 10px;
}

#searchInput {
    padding: 8px 12px;
    border: 1px solid var(--neon-blue);
    border-radius: 4px;
    width: 300px;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--neon-white);
    box-shadow: var(--glow-blue);
}

#sortSelect {
    padding: 8px 12px;
    border: 1px solid var(--neon-blue);
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--neon-white);
    box-shadow: var(--glow-blue);
}

.complaints-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.complaint-row {
    display: flex;
    background: var(--dark-bg);
    border: 1px solid var(--neon-blue);
    border-radius: 8px;
    padding: 20px;
    gap: 20px;
    transition: all 0.3s ease;
    box-shadow: var(--glow-blue);
}

.complaint-row:hover {
    background-color: rgba(0, 243, 255, 0.05);
    transform: translateY(-2px);
}

.complaint-votes {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 40px;
    padding: 0 4px;
}

.vote-btn {
    border: none;
    background: none;
    cursor: pointer;
    padding: 5px 10px;
    color: #666;
    transition: color 0.2s;
}

.vote-btn:hover {
    color: #888;
}

.vote-btn.upvote.voted {
    color: #ff4500;
}

.vote-btn.downvote.voted {
    color: #7193ff;
}

.vote-count {
    margin: 4px 0;
    font-weight: bold;
    color: #fff;
}

.complaint-main {
    flex: 1;
}

.complaint-header {
    margin-bottom: 10px;
}

.title-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.title-status h3 {
    margin: 0;
    font-size: 1.2em;
    color: var(--neon-orange);
    text-shadow: var(--glow-orange);
}

.complaint-meta {
    display: flex;
    gap: 15px;
    font-size: 0.9em;
    color: var(--neon-white);
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: 500;
}

.status-badge.pending { 
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border: 1px solid #ffc107;
}
.status-badge.approved { 
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid #28a745;
}
.status-badge.denied { 
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border: 1px solid #dc3545;
}

.complaint-content {
    margin: 15px 0;
    line-height: 1.6;
    white-space: pre-wrap;
    word-break: break-word;
}

.complaint-details {
    flex: 1;
    padding: 0 20px;
    border-left: 1px solid var(--neon-blue);
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
}

.detail-item label {
    color: var(--neon-blue);
    font-weight: bold;
}

.zkill-link {
    color: var(--neon-blue);
    text-decoration: none;
    padding: 5px 10px;
    border: 1px solid var(--neon-blue);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.zkill-link:hover {
    background: var(--neon-blue);
    color: var(--dark-bg);
}

.rage-level {
    color: var(--neon-orange) !important;
    text-shadow: var(--glow-orange);
    font-weight: 500;
}

.complaint-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 20px;
}

.read-more {
    padding: 8px 16px;
    background: transparent;
    color: var(--neon-blue);
    text-decoration: none;
    border-radius: 4px;
    font-size: 0.9em;
    transition: all 0.3s ease;
    white-space: nowrap;
    border: 1px solid var(--neon-blue);
    box-shadow: var(--glow-blue);
}

.read-more:hover {
    background: var(--neon-blue);
    color: var(--dark-bg);
}

.empty-message {
    text-align: center;
    padding: 40px;
    color: var(--neon-white);
    font-style: italic;
    opacity: 0.7;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .complaint-row {
        flex-direction: column;
    }

    .complaint-details {
        border-left: none;
        border-top: 1px solid var(--neon-blue);
        padding: 20px 0 0;
        margin-top: 20px;
    }
}
