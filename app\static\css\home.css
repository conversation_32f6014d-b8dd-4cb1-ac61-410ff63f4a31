/* Home page styling */
.home-container {
    max-width: 1200px;
    margin: 0 auto;
}

/* Welcome section */
.welcome-section {
    text-align: center;
    margin-bottom: 40px;
}

.welcome-section h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 10px;
}

.tagline {
    font-size: 1.2rem;
    color: #7f8c8d;
    font-style: italic;
}

/* Ranch description */
.ranch-description {
    margin-bottom: 50px;
}

.ranch-description h2 {
    font-size: 1.8rem;
    color: #2c3e50;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.ranch-description > p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 30px;
}

/* Feature cards */
.ranch-features {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 40px;
}

.feature-card {
    flex: 1;
    min-width: 250px;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.feature-card h3 {
    font-size: 1.4rem;
    color: #2c3e50;
    margin-bottom: 15px;
}

.feature-card p {
    margin-bottom: 20px;
    color: #555;
}

.feature-btn {
    display: inline-block;
    background-color: #0070ba;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    border: none;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s;
}

.feature-btn:hover {
    background-color: #005ea6;
}

/* Quote styling */
.ranch-quote {
    background-color: #f1f9ff;
    border-left: 4px solid #3498db;
    padding: 20px;
    margin: 30px 0;
    border-radius: 0 8px 8px 0;
}

.ranch-quote blockquote {
    font-size: 1.2rem;
    font-style: italic;
    color: #34495e;
    margin: 0 0 10px 0;
}

.ranch-quote cite {
    display: block;
    text-align: right;
    font-size: 0.9rem;
    color: #7f8c8d;
}

/* EVE Online info section */
.eve-info {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 30px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.eve-info h2 {
    color: #3498db;
    margin-bottom: 20px;
}

.eve-info p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.eve-link {
    display: inline-block;
    background-color: #0070ba; /* Changed to match the blue of feature-btn */
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    text-decoration: none;
    margin-top: 15px;
    transition: background-color 0.3s;
}

.eve-link:hover {
    background-color: #005ea6; /* Darker blue on hover to match feature-btn */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .ranch-features {
        flex-direction: column;
    }
    
    .feature-card {
        width: 100%;
    }
}
