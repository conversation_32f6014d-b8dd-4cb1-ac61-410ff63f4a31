from flask import Flask, request
from datetime import datetime
from app.config import Config
from app.extensions import db
from app.routes.main import main
from app.routes.complaints import complaints
from app.routes.admin import admin
from app.routes.auth import auth
from app.utils.bot_manager import initialize_bot, init_flask_app
from app.models.models import Visitor, Topic, Vote, Comment
import sqlalchemy as sa

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Initialize extensions
    db.init_app(app)

    # Register blueprints
    app.register_blueprint(main)
    app.register_blueprint(complaints)
    app.register_blueprint(admin)
    app.register_blueprint(auth)

    # Create database tables and handle schema changes
    with app.app_context():
        inspector = sa.inspect(db.engine)
        
        # Get existing tables
        existing_tables = inspector.get_table_names()
        
        # Create missing tables
        if 'topic' not in existing_tables:
            Topic.__table__.create(db.engine)
        else:
            # Add missing columns to existing topic table
            existing_columns = {col['name'] for col in inspector.get_columns('topic')}
            
            with db.engine.begin() as conn:
                if 'upvotes' not in existing_columns:
                    conn.execute(sa.text('ALTER TABLE topic ADD COLUMN upvotes INTEGER DEFAULT 0'))
                if 'downvotes' not in existing_columns:
                    conn.execute(sa.text('ALTER TABLE topic ADD COLUMN downvotes INTEGER DEFAULT 0'))
                
        # Create other tables if they don't exist
        if 'vote' not in existing_tables:
            Vote.__table__.create(db.engine)
        if 'visitor' not in existing_tables:
            Visitor.__table__.create(db.engine)
        if 'comment' not in existing_tables:
            Comment.__table__.create(db.engine)

        # Commit the transaction
        db.session.commit()

    # Initialize bot manager with Flask app
    init_flask_app(app)
    
    # Initialize Discord bot
    initialize_bot()

    @app.before_request
    def track_visitor():
        if request.endpoint != 'static':  # Don't track static file requests
            ip = request.remote_addr
            visitor = Visitor.query.filter_by(ip_address=ip).first()
            
            if visitor:
                visitor.last_visit = datetime.utcnow()
                visitor.visit_count += 1
            else:
                visitor = Visitor(ip_address=ip)
                db.session.add(visitor)
            
            try:
                db.session.commit()
            except:
                db.session.rollback()

    return app
