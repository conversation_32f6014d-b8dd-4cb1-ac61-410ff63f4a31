{"complaintTypes": [{"text": "Game Balance Issue", "value": "game_balance_issue"}, {"text": "UI/UX Complaint", "value": "ui_ux_complaint"}, {"text": "Server Performance", "value": "server_performance"}, {"text": "PvP Mechanics", "value": "pvp_mechanics"}, {"text": "Economic Changes", "value": "economic_changes"}], "blameOptions": [{"text": "CCP Games (obviously)", "value": "ccp_games_obviously_"}, {"text": "My Corp Leadership", "value": "my_corp_leadership"}, {"text": "Those <PERSON>n Null Sec Blocs", "value": "those_darn_null_sec_blocs"}, {"text": "The Market Manipulators", "value": "the_market_manipulators"}, {"text": "My Own Poor Life Choices", "value": "my_own_poor_life_choices"}, {"text": "RNG <PERSON>", "value": "rng_jesus"}], "rageLevels": [{"text": "1 - <PERSON><PERSON> Disappointment", "value": "1_mild_disappointment"}, {"text": "3 - <PERSON> Mumbling", "value": "3_angry_mumbling"}, {"text": "5 - Writing an Angry Forum Post", "value": "5_writing_an_angry_forum_post"}, {"text": "7 - Keyboard Smashing", "value": "7_keyboard_smashing"}, {"text": "9 - Uninstalling the Game", "value": "9_uninstalling_the_game"}, {"text": "10 - Creating a Reddit Megathread", "value": "10_creating_a_reddit_megathread"}], "quittingOptions": [{"text": "Just Until Tomorrow", "value": "just_until_tomorrow"}, {"text": "One Week Tops", "value": "one_week_tops"}, {"text": "Until the Next Patch", "value": "until_the_next_patch"}, {"text": "Until My Corp Needs Me", "value": "until_my_corp_needs_me"}, {"text": "Until I See a Cool Ship Screenshot", "value": "until_i_see_a_cool_ship_screenshot"}, {"text": "This Time I Mean It (Sure...)", "value": "this_time_i_mean_it_sure_"}]}