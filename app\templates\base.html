<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">                                                  <!-- Sets character encoding -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">  <!-- Makes site responsive -->
    <title>{{ title }} - Sky Daddy Ranch</title>                           <!-- Dynamic page title using Jinja2 variable -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">  <!-- Links to CSS file using Flask's url_for helper -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/forums.css') }}">
    
    <!-- Favicon code -->
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='style/favicon/favicon-96x96.png') }}" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='style/favicon/favicon.svg') }}" />
    <link rel="shortcut icon" href="{{ url_for('static', filename='style/favicon/favicon.ico') }}" />
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='style/favicon/apple-touch-icon.png') }}" />
    <meta name="apple-mobile-web-app-title" content="Sky Daddy Ranch" />
    <link rel="manifest" href="{{ url_for('static', filename='style/favicon/site.webmanifest') }}" />
    <style>
        .logout-button {
            padding: 8px 16px;
            background-color: #dc3545;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-right: 10px;
            transition: background-color 0.3s;
        }

        .logout-button:hover {
            background-color: #c82333;
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <!-- Navigation bar -->
    <div class="navbar">
        <!-- Navigation links with conditional class based on current page -->
        <a href="/" {% if title == 'Home' %}class="active"{% endif %}>Home</a>
        <a href="/complaints" {% if title == 'File Complaint' %}class="active"{% endif %}>File a Complaint</a>
        <a href="/forums" {% if title == 'Forums' %}class="active"{% endif %}>Forums</a>
        <a href="/login" {% if title == 'Login' %}class="active"{% endif %}>Login</a>
        
        {% if session.get('admin_logged_in') %}
        <a href="/admin/dashboard" {% if title == 'Admin Dashboard' %}class="active"{% endif %}>Admin Panel</a>
        {% endif %}
        
        <!-- Donation buttons aligned to the right -->
        <div class="navbar-right">
            {% if session.get('admin_logged_in') %}
            <a href="{{ url_for('admin.admin_logout') }}" class="logout-button">Logout</a>
            {% endif %}
            
            <!-- ISK Donation button -->
            <button type="button" class="isk-button" onclick="showIskDonationPopup()">
                <span class="isk-text">Donate ISK</span>
            </button>
            
            <!-- PayPal button (does nothing) -->
            <button type="button" class="paypal-button">
                <span class="paypal-text">Donate</span>
                <span class="paypal-logo">PayPal</span>
            </button>
        </div>
    </div>
    
    <!-- ISK Donation Popup -->
    <div id="iskDonationPopup" class="isk-modal">
        <div class="isk-modal-content">
            <span class="isk-close-btn" onclick="closeIskDonationPopup()">×</span>
            <h3>Donate ISK</h3>
            <p>Send ISK donations to:</p>
            <div class="character-name">Boshinu Amarr</div>
            <button class="copy-btn" onclick="copyToClipboard('Boshinu Amarr')">Copy Character Name</button>
        </div>
    </div>

    <style>
        .isk-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
        }

        .isk-modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 500px;
            border-radius: 5px;
            position: relative;
        }

        .isk-close-btn {
            position: absolute;
            right: 10px;
            top: 5px;
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .isk-close-btn:hover {
            color: black;
        }
    </style>

    <!-- Main container for page content -->
    <div class="container">
        <div class="content">
            {% block content %}{% endblock %}  <!-- Jinja2 block that child templates will fill -->
        </div>
    </div>

    <!-- Scripts -->
    <script>
        function showIskDonationPopup() {
            document.getElementById('iskDonationPopup').style.display = 'block';
        }
        
        function closeIskDonationPopup() {
            document.getElementById('iskDonationPopup').style.display = 'none';
        }
        
        // Function to copy character name to clipboard
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('Character name copied to clipboard: ' + text);
            }, function(err) {
                console.error('Could not copy text: ', err);
            });
        }
        
        // Close the popup if the user clicks outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('iskDonationPopup');
            if (event.target === modal) {
                closeIskDonationPopup();
            }
        }
    </script>
    {% if session.get('admin_logged_in') and title == 'Admin Dashboard' %}
        <!-- Admin-specific scripts -->
        <script src="{{ url_for('static', filename='js/admin.js') }}"></script>
    {% endif %}
</body>
</html>
