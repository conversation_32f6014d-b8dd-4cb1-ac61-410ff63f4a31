/* Add this to center the header text */
.header-center {
    text-align: center;
    margin-bottom: 20px;
}

.header-center h1 {
    margin-bottom: 2px; /* Reduced from 5px to 2px */
}

/* Existing CSS continues below */
/* EVE Online Complaint Form Styling */
.complaint-form {
    max-width: 800px;
    margin: 0 auto;
}

/* New complaint container for layout */
.complaint-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin: 20px 0;
}

/* HR Department styling */
.hr-department {
    flex: 1;
    min-width: 300px;
    max-width: 400px;
    background-color: #1a1a2e;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-left: 4px solid #d9534f;
}

.hr-department h2 {
    color: #d9534f;
    border-bottom: 2px solid #d9534f;
    padding-bottom: 10px;
    margin-top: 0;
}

.hr-motto {
    background-color: rgba(217, 83, 79, 0.1);
    border-left: 3px solid #d9534f;
    padding: 15px;
    margin: 15px 0;
}

.hr-motto blockquote {
    font-style: italic;
    font-size: 1.1em;
    margin: 0;
    color: #f8f8f8;
}

.hr-message h3 {
    color: #5bc0de;
    margin-top: 20px;
    margin-bottom: 10px;
}

.hr-message p {
    color: #f0f0f0;
    line-height: 1.5;
}

.hr-guarantee-list {
    list-style-type: none;
    padding: 0;
    margin: 15px 0;
}

.hr-guarantee-list li {
    margin-bottom: 10px;
    color: #f0f0f0;
    display: flex;
    align-items: flex-start;
}

.guarantee-icon {
    margin-right: 10px;
    font-size: 1.2em;
}

.hr-testimonial {
    background-color: rgba(91, 192, 222, 0.1);
    border-left: 3px solid #5bc0de;
    padding: 15px;
    margin: 20px 0;
}

.hr-testimonial p {
    font-style: italic;
    margin: 0 0 10px 0;
    color: #f8f8f8;
}

.hr-testimonial cite {
    font-size: 0.9em;
    color: #aaa;
}

.hr-disclaimer {
    font-size: 0.8em;
    color: #888;
    margin-top: 20px;
    border-top: 1px dashed #444;
    padding-top: 10px;
}

/* Complaint form styling */
.complaint-form {
    flex: 2;
    min-width: 450px;
}

.complaint-form h1 {
    color: #d9534f;
    border-bottom: 2px solid #d9534f;
    padding-bottom: 10px;
}

.complaint-form .btn {
    background-color: #d9534f;
}

.complaint-form .btn:hover {
    background-color: #c9302c;
}

/* Responsive adjustments */
@media (max-width: 900px) {
    .complaint-container {
        flex-direction: column;
    }
    
    .hr-department, .complaint-form {
        max-width: 100%;
    }
}

/* Specific styling for complaint elements */
.complaint-type-label {
    font-weight: bold;
    color: #d9534f;
}

.rage-meter {
    margin: 15px 0;
}

/* Forum complaint display styling */
.complaint-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin: 20px 0;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #f0f0f0;
}

.detail-item i {
    width: 20px;
    text-align: center;
    color: #666;
}

.detail-item strong {
    min-width: 120px;
    color: #888;
}

/* Tags styling */
.complaint-tag, .blame-tag, .quitting-tag, .zkill-link {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
    font-weight: 500;
}

.complaint-tag {
    background-color: #2c3e50;
    color: #ecf0f1;
}

.blame-tag {
    background-color: #c0392b;
    color: #ecf0f1;
}

.quitting-tag {
    padding: 4px 8px;
    border-radius: 4px;
}

.quitting-yes {
    background-color: #c0392b;
    color: #ecf0f1;
}

.quitting-no {
    background-color: #27ae60;
    color: #ecf0f1;
}

.zkill-link {
    background-color: #8e44ad;
    color: #ecf0f1;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: background-color 0.2s ease;
}

.zkill-link:hover {
    background-color: #9b59b6;
    color: #ecf0f1;
    text-decoration: none;
}

/* Rage level styling */
.rage-level {
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.rage-low {
    background-color: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
}

.rage-medium {
    background-color: rgba(241, 196, 15, 0.2);
    color: #f1c40f;
}

.rage-high {
    background-color: rgba(230, 126, 34, 0.2);
    color: #e67e22;
}

.rage-extreme {
    background-color: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

/* Complaint meta styling */
.complaint-meta {
    display: flex;
    gap: 15px;
    margin: 10px 0;
    padding-bottom: 15px;
    border-bottom: 1px solid #333;
}

.complaint-meta span {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #888;
}

.complaint-meta i {
    color: #666;
}

.zkill-container {
    margin: 10px 0;
}

.zkill-link {
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    color: #d9534f;
    font-weight: 500;
}

.zkill-link:hover {
    text-decoration: none;
    opacity: 0.9;
}

.zkill-icon {
    vertical-align: middle;
    height: 16px;
    width: 16px;
    margin-right: 4px;
}

.zkill-row {
    margin-top: 10px;
}

/* zKillboard link verification styling */
.zkill-container {
    position: relative;
}

.zkill-input-container {
    display: flex;
    align-items: center;
}

.verification-icon {
    width: 20px;
    height: 20px;
    margin-left: 10px;
    border-radius: 50%;
    display: inline-block;
}

.verification-icon.verified {
    background-color: #28a745;
    position: relative;
}

.verification-icon.verified::before {
    content: "✓";
    color: white;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
}

.verification-icon.invalid {
    background-color: #dc3545;
    position: relative;
}

.verification-icon.invalid::before {
    content: "✗";
    color: white;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
}

/* Styling for the reimbursement dropdown */
#reimbursement option:disabled {
    color: #999;
    font-style: italic;
}

.form-text.text-muted {
    font-size: 0.85em;
    color: #6c757d;
    margin-top: 5px;
    font-style: italic;
}

/* Add a subtle red border to the reimbursement dropdown for extra salt */
#reimbursement {
    border: 1px solid #ffcccc;
    background-color: #fff9f9;
}

/* Add a hover effect that makes disabled options briefly appear available before reverting */
#reimbursement:hover option:disabled {
    color: #333;
    animation: tease 0.5s forwards;
}

@keyframes tease {
    0% { color: #333; }
    80% { color: #333; }
    100% { color: #999; }
}

/* Message content styling */
.message-content {
    white-space: pre-wrap;
    word-break: break-word;
    font-family: inherit;
    background-color: #f8f8f8;
    padding: 15px;
    border-radius: 5px;
    margin: 15px 0;
    border-left: 3px solid #5bc0de;
}

.topic-preview {
    white-space: pre-wrap;
    word-break: break-word;
}

/* Add this to the end of the file to override the complaint details in the popup */
.modal-content .complaint-full-details {
    background-color: #333333;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 15px;
}

.modal-content .complaint-detail-row {
    display: flex;
    margin-bottom: 5px;
    color: #f0f0f0;
}

.modal-content .detail-label {
    font-weight: bold;
    width: 120px;
    color: #aaa;
}

.modal-content .complaint-tag, 
.modal-content .blame-tag, 
.modal-content .rage-level, 
.modal-content .quitting-time {
    background-color: #444444;
    color: #f0f0f0;
}

.modal-content .message-content {
    background-color: #2a2a2a;
    color: #f0f0f0;
    border-left: 3px solid #4CAF50;
}

/* Duplicate submission styling */
.duplicate-submission-container {
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background-color: #1a1a2e;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.duplicate-message h1 {
    color: #d9534f;
    border-bottom: 2px solid #d9534f;
    padding-bottom: 10px;
    margin-top: 0;
}

.message-box {
    padding: 15px;
    margin: 20px 0;
    border-radius: 5px;
    font-size: 1.1em;
}

.message-box.error {
    background-color: rgba(217, 83, 79, 0.1);
    border-left: 3px solid #d9534f;
    color: #f8f8f8;
}

.options {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

.options .btn {
    flex: 1;
    text-align: center;
}

.options .btn.secondary {
    background-color: #5bc0de;
}

.options .btn.secondary:hover {
    background-color: #46b8da;
}

.complaints-list {
    margin: 20px 0;
}

.complaint-card {
    background-color: #2a2a2a;
    border: 1px solid #3a3a3a;
    border-left: 4px solid #d9534f;
    margin-bottom: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
    padding: 15px;
    cursor: pointer;
}

.complaint-card:hover {
    background-color: #333333;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    border-color: #d9534f;
    transform: translateY(-2px);
}

.complaint-title {
    color: #d9534f;
    margin: 0 0 10px 0;
    font-size: 1.2em;
}

.complaint-meta {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9em;
    color: #888;
    margin-bottom: 8px;
}

.complaint-preview {
    color: #ccc;
    font-size: 0.95em;
    line-height: 1.4;
}

.rage-level {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.85em;
    background-color: rgba(217, 83, 79, 0.2);
    color: #d9534f;
}

.rage-level i {
    font-size: 0.9em;
}

.complaint-message {
    color: #f0f0f0;
    white-space: pre-wrap;
    word-break: break-word;
    background-color: #222222;
    padding: 10px;
    border-radius: 4px;
    border-left: 3px solid #5bc0de;
}

.complaint-author {
    font-weight: 500;
    color: #f0f0f0;
}

.complaint-date {
    color: #888888;
}

.zkill-link {
    background-color: #28a745;
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    text-decoration: none;
    font-size: 0.85em;
    transition: background-color 0.2s ease;
}

.zkill-link:hover {
    background-color: #218838;
    text-decoration: none;
    color: white;
}

.no-complaints {
    color: #f0f0f0;
    text-align: center;
    padding: 20px;
    background-color: #2a2a2a;
    border-radius: 8px;
    border: 1px solid #3a3a3a;
}

.error-message {
    color: #d9534f;
    text-align: center;
    padding: 20px;
    background-color: #2a2a2a;
    border-radius: 8px;
    border: 1px solid #d9534f;
}

.share-buttons {
    display: flex;
    justify-content: center;
    margin: 1rem 0;
}

.share-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #fff;
    background-color: #2c3e50;
}

.share-btn:hover {
    background-color: #34495e;
}

.share-btn i {
    font-size: 16px;
}

.copy-btn {
    background-color: #2c3e50;
}

.copy-btn:hover {
    background-color: #34495e;
}

.copy-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 24px;
    background-color: #333;
    color: white;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateX(120%);
    transition: transform 0.3s ease-in-out;
    z-index: 9999;
}

.copy-notification.show {
    transform: translateX(0);
}

.copy-notification .notification-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.copy-notification i {
    font-size: 16px;
}

.copy-notification.success {
    background-color: #2ecc71;
}

.copy-notification.error {
    background-color: #e74c3c;
}

/* Custom scrollbar for the popup content */
.complaint-popup-content {
    /* Ensure existing styles are preserved */
    scrollbar-width: thin; /* For Firefox */
    scrollbar-color: #666 #1a1a1a; /* For Firefox */
}

/* Webkit browsers (Chrome, Safari, Edge) scrollbar styling */
.complaint-popup-content::-webkit-scrollbar {
    width: 8px;
}

.complaint-popup-content::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 4px;
}

.complaint-popup-content::-webkit-scrollbar-thumb {
    background: #666;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.complaint-popup-content::-webkit-scrollbar-thumb:hover {
    background: #888;
}

/* Optional: Style the scrollbar corner */
.complaint-popup-content::-webkit-scrollbar-corner {
    background: #1a1a1a;
}

/* Ensure smooth scrolling */
.complaint-popup-content {
    scroll-behavior: smooth;
}

/* Add this to center the header text */
.header-center {
    text-align: center;
    margin-bottom: 20px;
}

.header-center h1 {
    margin-bottom: 2px; /* Reduced from 5px to 2px */
}

/* Existing CSS continues below */
/* EVE Online Complaint Form Styling */
.complaint-form {
    max-width: 800px;
    margin: 0 auto;
}

/* New complaint container for layout */
.complaint-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin: 20px 0;
}

/* HR Department styling */
.hr-department {
    flex: 1;
    min-width: 300px;
    max-width: 400px;
    background-color: #1a1a2e;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-left: 4px solid #d9534f;
}

.hr-department h2 {
    color: #d9534f;
    border-bottom: 2px solid #d9534f;
    padding-bottom: 10px;
    margin-top: 0;
}

.hr-motto {
    background-color: rgba(217, 83, 79, 0.1);
    border-left: 3px solid #d9534f;
    padding: 15px;
    margin: 15px 0;
}

.hr-motto blockquote {
    font-style: italic;
    font-size: 1.1em;
    margin: 0;
    color: #f8f8f8;
}

.hr-message h3 {
    color: #5bc0de;
    margin-top: 20px;
    margin-bottom: 10px;
}

.hr-message p {
    color: #f0f0f0;
    line-height: 1.5;
}

.hr-guarantee-list {
    list-style-type: none;
    padding: 0;
    margin: 15px 0;
}

.hr-guarantee-list li {
    margin-bottom: 10px;
    color: #f0f0f0;
    display: flex;
    align-items: flex-start;
}

.guarantee-icon {
    margin-right: 10px;
    font-size: 1.2em;
}

.hr-testimonial {
    background-color: rgba(91, 192, 222, 0.1);
    border-left: 3px solid #5bc0de;
    padding: 15px;
    margin: 20px 0;
}

.hr-testimonial p {
    font-style: italic;
    margin: 0 0 10px 0;
    color: #f8f8f8;
}

.hr-testimonial cite {
    font-size: 0.9em;
    color: #aaa;
}

.hr-disclaimer {
    font-size: 0.8em;
    color: #888;
    margin-top: 20px;
    border-top: 1px dashed #444;
    padding-top: 10px;
}

/* Complaint form styling */
.complaint-form {
    flex: 2;
    min-width: 450px;
}

.complaint-form h1 {
    color: #d9534f;
    border-bottom: 2px solid #d9534f;
    padding-bottom: 10px;
}

.complaint-form .btn {
    background-color: #d9534f;
}

.complaint-form .btn:hover {
    background-color: #c9302c;
}

/* Responsive adjustments */
@media (max-width: 900px) {
    .complaint-container {
        flex-direction: column;
    }
    
    .hr-department, .complaint-form {
        max-width: 100%;
    }
}

/* Specific styling for complaint elements */
.complaint-type-label {
    font-weight: bold;
    color: #d9534f;
}

.rage-meter {
    margin: 15px 0;
}

/* Forum complaint display styling */
.complaint-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin: 20px 0;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #f0f0f0;
}

.detail-item i {
    width: 20px;
    text-align: center;
    color: #666;
}

.detail-item strong {
    min-width: 120px;
    color: #888;
}

/* Tags styling */
.complaint-tag, .blame-tag, .quitting-tag, .zkill-link {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
    font-weight: 500;
}

.complaint-tag {
    background-color: #2c3e50;
    color: #ecf0f1;
}

.blame-tag {
    background-color: #c0392b;
    color: #ecf0f1;
}

.quitting-tag {
    padding: 4px 8px;
    border-radius: 4px;
}

.quitting-yes {
    background-color: #c0392b;
    color: #ecf0f1;
}

.quitting-no {
    background-color: #27ae60;
    color: #ecf0f1;
}

.zkill-link {
    background-color: #8e44ad;
    color: #ecf0f1;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: background-color 0.2s ease;
}

.zkill-link:hover {
    background-color: #9b59b6;
    color: #ecf0f1;
    text-decoration: none;
}

/* Rage level styling */
.rage-level {
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.rage-low {
    background-color: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
}

.rage-medium {
    background-color: rgba(241, 196, 15, 0.2);
    color: #f1c40f;
}

.rage-high {
    background-color: rgba(230, 126, 34, 0.2);
    color: #e67e22;
}

.rage-extreme {
    background-color: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

/* Complaint meta styling */
.complaint-meta {
    display: flex;
    gap: 15px;
    margin: 10px 0;
    padding-bottom: 15px;
    border-bottom: 1px solid #333;
}

.complaint-meta span {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #888;
}

.complaint-meta i {
    color: #666;
}

.zkill-container {
    margin: 10px 0;
}

.zkill-link {
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    color: #d9534f;
    font-weight: 500;
}

.zkill-link:hover {
    text-decoration: none;
    opacity: 0.9;
}

.zkill-icon {
    vertical-align: middle;
    height: 16px;
    width: 16px;
    margin-right: 4px;
}

.zkill-row {
    margin-top: 10px;
}

/* zKillboard link verification styling */
.zkill-container {
    position: relative;
}

.zkill-input-container {
    display: flex;
    align-items: center;
}

.verification-icon {
    width: 20px;
    height: 20px;
    margin-left: 10px;
    border-radius: 50%;
    display: inline-block;
}

.verification-icon.verified {
    background-color: #28a745;
    position: relative;
}

.verification-icon.verified::before {
    content: "✓";
    color: white;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
}

.verification-icon.invalid {
    background-color: #dc3545;
    position: relative;
}

.verification-icon.invalid::before {
    content: "✗";
    color: white;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
}

/* Styling for the reimbursement dropdown */
#reimbursement option:disabled {
    color: #999;
    font-style: italic;
}

.form-text.text-muted {
    font-size: 0.85em;
    color: #6c757d;
    margin-top: 5px;
    font-style: italic;
}

/* Add a subtle red border to the reimbursement dropdown for extra salt */
#reimbursement {
    border: 1px solid #ffcccc;
    background-color: #fff9f9;
}

/* Add a hover effect that makes disabled options briefly appear available before reverting */
#reimbursement:hover option:disabled {
    color: #333;
    animation: tease 0.5s forwards;
}

@keyframes tease {
    0% { color: #333; }
    80% { color: #333; }
    100% { color: #999; }
}

/* Message content styling */
.message-content {
    white-space: pre-wrap;
    word-break: break-word;
    font-family: inherit;
    background-color: #f8f8f8;
    padding: 15px;
    border-radius: 5px;
    margin: 15px 0;
    border-left: 3px solid #5bc0de;
}

.topic-preview {
    white-space: pre-wrap;
    word-break: break-word;
}

/* Add this to the end of the file to override the complaint details in the popup */
.modal-content .complaint-full-details {
    background-color: #333333;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 15px;
}

.modal-content .complaint-detail-row {
    display: flex;
    margin-bottom: 5px;
    color: #f0f0f0;
}

.modal-content .detail-label {
    font-weight: bold;
    width: 120px;
    color: #aaa;
}

.modal-content .complaint-tag, 
.modal-content .blame-tag, 
.modal-content .rage-level, 
.modal-content .quitting-time {
    background-color: #444444;
    color: #f0f0f0;
}

.modal-content .message-content {
    background-color: #2a2a2a;
    color: #f0f0f0;
    border-left: 3px solid #4CAF50;
}

/* Duplicate submission styling */
.duplicate-submission-container {
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background-color: #1a1a2e;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.duplicate-message h1 {
    color: #d9534f;
    border-bottom: 2px solid #d9534f;
    padding-bottom: 10px;
    margin-top: 0;
}

.message-box {
    padding: 15px;
    margin: 20px 0;
    border-radius: 5px;
    font-size: 1.1em;
}

.message-box.error {
    background-color: rgba(217, 83, 79, 0.1);
    border-left: 3px solid #d9534f;
    color: #f8f8f8;
}

.options {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

.options .btn {
    flex: 1;
    text-align: center;
}

.options .btn.secondary {
    background-color: #5bc0de;
}

.options .btn.secondary:hover {
    background-color: #46b8da;
}

.complaints-list {
    margin: 20px 0;
}

.complaint-card {
    background-color: #2a2a2a;
    border: 1px solid #3a3a3a;
    border-left: 4px solid #d9534f;
    margin-bottom: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
    padding: 15px;
    cursor: pointer;
}

.complaint-card:hover {
    background-color: #333333;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    border-color: #d9534f;
    transform: translateY(-2px);
}

.complaint-title {
    color: #d9534f;
    margin: 0 0 10px 0;
    font-size: 1.2em;
}

.complaint-meta {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9em;
    color: #888;
    margin-bottom: 8px;
}

.complaint-preview {
    color: #ccc;
    font-size: 0.95em;
    line-height: 1.4;
}

.rage-level {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.85em;
    background-color: rgba(217, 83, 79, 0.2);
    color: #d9534f;
}

.rage-level i {
    font-size: 0.9em;
}

.complaint-message {
    color: #f0f0f0;
    white-space: pre-wrap;
    word-break: break-word;
    background-color: #222222;
    padding: 10px;
    border-radius: 4px;
    border-left: 3px solid #5bc0de;
}

.complaint-author {
    font-weight: 500;
    color: #f0f0f0;
}

.complaint-date {
    color: #888888;
}

.zkill-link {
    background-color: #28a745;
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    text-decoration: none;
    font-size: 0.85em;
    transition: background-color 0.2s ease;
}

.zkill-link:hover {
    background-color: #218838;
    text-decoration: none;
    color: white;
}

.no-complaints {
    color: #f0f0f0;
    text-align: center;
    padding: 20px;
    background-color: #2a2a2a;
    border-radius: 8px;
    border: 1px solid #3a3a3a;
}

.error-message {
    color: #d9534f;
    text-align: center;
    padding: 20px;
    background-color: #2a2a2a;
    border-radius: 8px;
    border: 1px solid #d9534f;
}

.share-buttons {
    display: flex;
    justify-content: center;
    margin: 1rem 0;
}

.share-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #fff;
    background-color: #2c3e50;
}

.share-btn:hover {
    background-color: #34495e;
}

.share-btn i {
    font-size: 16px;
}

.copy-btn {
    background-color: #2c3e50;
}

.copy-btn:hover {
    background-color: #34495e;
}

.copy-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 24px;
    background-color: #333;
    color: white;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateX(120%);
    transition: transform 0.3s ease-in-out;
    z-index: 9999;
}

.copy-notification.show {
    transform: translateX(0);
}

.copy-notification .notification-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.copy-notification i {
    font-size: 16px;
}

.copy-notification.success {
    background-color: #2ecc71;
}

.copy-notification.error {
    background-color: #e74c3c;
}

/* Custom scrollbar for the popup content */
.complaint-popup-content {
    /* Ensure existing styles are preserved */
    scrollbar-width: thin; /* For Firefox */
    scrollbar-color: #666 #1a1a1a; /* For Firefox */
}

/* Webkit browsers (Chrome, Safari, Edge) scrollbar styling */
.complaint-popup-content::-webkit-scrollbar {
    width: 8px;
}

.complaint-popup-content::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 4px;
}

.complaint-popup-content::-webkit-scrollbar-thumb {
    background: #666;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.complaint-popup-content::-webkit-scrollbar-thumb:hover {
    background: #888;
}

/* Optional: Style the scrollbar corner */
.complaint-popup-content::-webkit-scrollbar-corner {
    background: #1a1a1a;
}

/* Ensure smooth scrolling */
.complaint-popup-content {
    scroll-behavior: smooth;
}
