/* Forum layout with sidebar */
.forum-layout {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.forum-main {
    flex: 1;
}

.forum-sidebar {
    width: 300px;
    margin-top: 40px;
}

.sidebar-section {
    background-color: #1a1a1a;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.sidebar-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
    color: #0070ba;
    border-bottom: 1px solid #333;
    padding-bottom: 8px;
}

.sidebar-item {
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 10px;
    background-color: #222;
    cursor: pointer;
    transition: background-color 0.2s;
}

.sidebar-item:hover {
    background-color: #2a2a2a;
}

.sidebar-item-title {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 0.9rem;
    color: #fff;
}

.sidebar-item-text {
    font-size: 0.85rem;
    color: #ccc;
    margin-bottom: 5px;
}

.sidebar-item-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #888;
}

.sidebar-upvotes, .sidebar-comments, .sidebar-author {
    display: inline-flex;
    align-items: center;
}

.sidebar-empty {
    color: #666;
    font-style: italic;
    font-size: 0.9rem;
}

/* Top forum container with search and top content */
.top-forum-container {
    margin-bottom: 20px;
}

.search-container {
    max-width: 500px;
    margin-bottom: 20px;
}

/* Top content container for posts and comments */
.top-content-container {
    display: flex;
    flex-direction: row; /* Explicitly set to row */
    justify-content: space-between; /* Space between the two sections */
    gap: 20px; /* Increased gap for better separation */
    margin-bottom: 30px;
    width: 100%; /* Ensure it takes full width */
}

.top-mini-section {
    flex: 1; /* Each section takes equal space */
    min-width: 0; /* Prevent flex items from overflowing */
    background-color: #1a1a1a;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.top-mini-section h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 0.9rem;
    color: #0070ba;
    border-bottom: 1px solid #333;
    padding-bottom: 5px;
}

.top-mini-item {
    padding: 5px;
    border-radius: 4px;
    margin-bottom: 5px;
    background-color: #222;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 0.8rem;
}

.top-mini-item:hover {
    background-color: #2a2a2a;
}

.mini-item-title, .mini-item-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #ccc;
    margin-bottom: 2px;
}

.mini-item-meta {
    font-size: 0.7rem;
    color: #888;
}

.mini-upvotes {
    display: inline-flex;
    align-items: center;
}

/* Make the layout responsive */
@media (max-width: 768px) {
    .top-content-container {
        flex-direction: column;
    }
    
    .top-mini-section {
        width: 100%;
    }
}

/* More specific rule to ensure side-by-side layout */
.forum-main .top-content-container {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
}

.forum-main .top-content-container .top-mini-section {
    flex: 1 1 0 !important;
    width: calc(50% - 10px) !important;
    box-sizing: border-box !important;
}

/* Floating overlay for top posts and comments */
.floating-top-content {
    position: fixed;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 300px;
    background-color: rgba(26, 26, 26, 0.95);
    border-radius: 8px 0 0 8px;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    transition: right 0.3s ease;
    overflow: hidden;
}

.floating-top-content.collapsed {
    right: -280px;
}

.floating-top-content .toggle-button {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background-color: #0070ba;
    color: white;
    border: none;
    border-radius: 4px 0 0 4px;
    padding: 10px 5px;
    cursor: pointer;
    writing-mode: vertical-lr;
    text-orientation: mixed;
    height: 120px;
    font-size: 0.9rem;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.2);
}

.floating-top-content .content-wrapper {
    padding: 15px 15px 15px 30px;
}

.floating-top-content .section-tabs {
    display: flex;
    margin-bottom: 10px;
    border-bottom: 1px solid #333;
}

.floating-top-content .tab {
    padding: 5px 10px;
    cursor: pointer;
    color: #888;
    font-size: 0.9rem;
    border-bottom: 2px solid transparent;
}

.floating-top-content .tab.active {
    color: #0070ba;
    border-bottom: 2px solid #0070ba;
}

.floating-top-content .tab-content {
    display: none;
}

.floating-top-content .tab-content.active {
    display: block;
}

.floating-top-content .mini-item {
    padding: 8px;
    border-radius: 4px;
    margin-bottom: 5px;
    background-color: #222;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 0.8rem;
}

.floating-top-content .mini-item:hover {
    background-color: #2a2a2a;
}

.floating-top-content .mini-item-title,
.floating-top-content .mini-item-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #ccc;
    margin-bottom: 2px;
}

.floating-top-content .mini-item-meta {
    font-size: 0.7rem;
    color: #888;
}

/* Make the overlay responsive */
@media (max-width: 768px) {
    .floating-top-content {
        width: 250px;
    }
    
    .floating-top-content.collapsed {
        right: -230px;
    }
}
