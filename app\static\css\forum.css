/* Forum Layout */
.forum-layout {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.forum-header {
    margin-bottom: 20px;
}

.forum-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.complaints-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Forum Controls */
.forum-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    padding: 15px;
    background: #1a1a1a;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

#searchInput {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #333;
    border-radius: 4px;
    background: #2a2a2a;
    color: #fff;
}

#sortBy {
    padding: 8px 12px;
    border: 1px solid #333;
    border-radius: 4px;
    background: #2a2a2a;
    color: #fff;
}

/* Reddit-style Complaint Cards */
.complaint-card {
    display: flex;
    gap: 15px;
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 15px;
}

.complaint-card:hover {
    background: #222;
}

/* Main Content Area */
.complaint-main {
    flex: 1;
}

.complaint-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.complaint-title {
    margin: 0;
    font-size: 1.2em;
}

.complaint-title a {
    color: #fff;
    text-decoration: none;
}

.complaint-title a:hover {
    color: #4CAF50;
}

/* Metadata */
.complaint-meta {
    font-size: 0.9em;
    color: #888;
    margin-bottom: 10px;
}

.complaint-author {
    color: #4CAF50;
}

.complaint-preview {
    margin-top: 8px;
    color: #ccc;
    font-size: 14px;
    line-height: 1.4;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* Tags and Indicators */
.complaint-tags {
    display: flex;
    gap: 8px;
    margin: 10px 0;
}

.tag {
    background: #333;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
}

.rage-icon {
    font-size: 1.2em;
}

/* Footer Actions */
.complaint-footer {
    display: flex;
    gap: 15px;
    margin-top: 10px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    background: none;
    border: none;
    color: #888;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
}

.action-btn:hover {
    background: #333;
}

.loading, .no-complaints, .error {
    text-align: center;
    padding: 20px;
    color: #888;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-top: 20px;
}

.page-btn {
    padding: 5px 10px;
    background: #333;
    border: none;
    border-radius: 4px;
    color: #fff;
    cursor: pointer;
}

.page-btn.active {
    background: #0070ba;
}

/* Responsive Design */
@media (max-width: 768px) {
    .forum-controls {
        flex-direction: column;
    }

    #searchInput, #sortBy {
        width: 100%;
    }

    .complaint-card {
        flex-direction: column;
    }
}
