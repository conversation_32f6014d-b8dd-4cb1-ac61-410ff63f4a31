{% extends "base.html" %}

{% block content %}
<div class="admin-dashboard">
    <h1>Sky Daddy Ranch Admin Panel</h1>
    
    <div class="admin-tabs">
        <button class="tab-button active" data-tab="complaints">Complaints</button>
        <button class="tab-button" data-tab="form">Complaint Form</button>
        <button class="tab-button" data-tab="status">Status</button>
    </div>

    <!-- Complaints Tab -->
    <div class="tab-content active" id="complaints-tab">
        <div class="search-filter-container">
            <form id="searchFilterForm" class="search-form">
                <div class="search-group">
                    <input type="text" name="query" id="searchQuery" placeholder="Search complaints...">
                    <button type="submit" class="btn search-btn">Search</button>
                </div>
                
                <div class="filters-group">
                    <select id="statusFilter" class="filter-select">
                        <option value="all">All Status</option>
                        <option value="pending">Pending</option>
                        <option value="approved">Approved</option>
                        <option value="denied">Denied</option>
                    </select>

                    <select id="sortBy" class="filter-select">
                        <option value="date">Most Recent</option>
                        <option value="votes">Most Votes</option>
                        <option value="rage">Highest Rage</option>
                    </select>
                </div>
            </form>
        </div>
        
        <div class="complaints-container">
            <div id="complaintsList" class="complaints-list">
                <p class="loading-message">Loading complaints...</p>
            </div>
        </div>
    </div>

    <!-- Complaint Form Tab -->
    <div class="tab-content" id="form-tab">
        <div class="form-options-container">
            <!-- Complaint Types Section -->
            <div class="option-section">
                <h3>Complaint Types</h3>
                <div id="complaintTypeOptions" class="options-list">
                    {% for option in form_options.complaint_types %}
                    <div class="option-item">
                        <input type="text" value="{{ option }}" class="option-input">
                        <button type="button" class="delete-option-btn" onclick="deleteOption(this)">Delete</button>
                    </div>
                    {% endfor %}
                </div>
                <button type="button" class="add-option-btn" onclick="addOption('complaintTypeOptions')">Add Complaint Type</button>
            </div>

            <!-- Blame Options Section -->
            <div class="option-section">
                <h3>Blame Options</h3>
                <div id="blameOptions" class="options-list">
                    {% for option in form_options.blame_options %}
                    <div class="option-item">
                        <input type="text" value="{{ option }}" class="option-input">
                        <button type="button" class="delete-option-btn" onclick="deleteOption(this)">Delete</button>
                    </div>
                    {% endfor %}
                </div>
                <button type="button" class="add-option-btn" onclick="addOption('blameOptions')">Add Blame Option</button>
            </div>

            <!-- Rage Levels Section -->
            <div class="option-section">
                <h3>Rage Levels</h3>
                <div id="rageLevelOptions" class="options-list">
                    {% for option in form_options.rage_levels %}
                    <div class="option-item">
                        <input type="text" value="{{ option }}" class="option-input">
                        <button type="button" class="delete-option-btn" onclick="deleteOption(this)">Delete</button>
                    </div>
                    {% endfor %}
                </div>
                <button type="button" class="add-option-btn" onclick="addOption('rageLevelOptions')">Add Rage Level</button>
            </div>

            <!-- Quitting Options Section -->
            <div class="option-section">
                <h3>Quitting Options</h3>
                <div id="quittingOptions" class="options-list">
                    {% for option in form_options.quitting_options %}
                    <div class="option-item">
                        <input type="text" value="{{ option }}" class="option-input">
                        <button type="button" class="delete-option-btn" onclick="deleteOption(this)">Delete</button>
                    </div>
                    {% endfor %}
                </div>
                <button type="button" class="add-option-btn" onclick="addOption('quittingOptions')">Add Quitting Option</button>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <button id="saveFormChanges" class="save-button">Save Changes</button>
                <button id="resetDefaults" class="btn secondary">Reset to Defaults</button>
            </div>
        </div>
    </div>

    <!-- Status Tab -->
    <div class="tab-content" id="status-tab">
        <div class="status-grid">
            <div class="status-card">
                <h3>System Status</h3>
                <div class="status-indicator online">
                    <span class="dot"></span>
                    <span>Online</span>
                </div>
                <div class="status-details">
                    <p>Uptime: <span id="uptime">99.9%</span></p>
                    <p>Last Incident: <span id="lastIncident">None</span></p>
                </div>
            </div>

            <div class="status-card">
                <h3>Complaint Processing</h3>
                <div class="processing-stats">
                    <div class="stat">
                        <label>Pending</label>
                        <span id="pendingCount">0</span>
                    </div>
                    <div class="stat">
                        <label>Approved</label>
                        <span id="approvedCount">0</span>
                    </div>
                    <div class="stat">
                        <label>Denied</label>
                        <span id="deniedCount">0</span>
                    </div>
                </div>
            </div>

            <div class="status-card">
                <h3>Response Times</h3>
                <div class="response-chart">
                    <canvas id="responseChart"></canvas>
                </div>
            </div>

            <div class="status-card">
                <h3>Site Traffic</h3>
                <div class="processing-stats">
                    <div class="stat">
                        <label>Total Visitors</label>
                        <span id="totalVisitors">0</span>
                    </div>
                    <div class="stat">
                        <label>Today's Visitors</label>
                        <span id="todayVisitors">0</span>
                    </div>
                    <div class="stat">
                        <label>Active Now</label>
                        <span id="activeVisitors">0</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Tab Switching Logic
document.querySelectorAll('.tab-button').forEach(button => {
    button.addEventListener('click', () => {
        // Remove active class from all tabs
        document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
        
        // Add active class to clicked tab
        button.classList.add('active');
        document.getElementById(`${button.dataset.tab}-tab`).classList.add('active');
        
        // Load tab-specific data
        if (button.dataset.tab === 'complaints') {
            loadComplaints();
        } else if (button.dataset.tab === 'status') {
            updateStatusData();
        }
    });
});

// Keep existing complaints loading logic
let currentFilters = {
    query: '',
    status: 'all',  // Changed default to 'all'
    sortBy: 'date'
};

// Add event listeners for search and filters
document.getElementById('searchFilterForm').addEventListener('submit', function(e) {
    e.preventDefault();
    currentFilters.query = document.getElementById('searchQuery').value.trim();
    loadComplaints();
});

document.getElementById('statusFilter').addEventListener('change', function(e) {
    currentFilters.status = e.target.value;
    loadComplaints();
});

document.getElementById('sortBy').addEventListener('change', function(e) {
    currentFilters.sortBy = e.target.value;
    loadComplaints();
});

// Function to update status data
function updateStatusData() {
    fetch('/api/admin/stats')
        .then(response => response.json())
        .then(data => {
            // Update existing stats
            document.getElementById('pendingCount').textContent = data.pending || 0;
            document.getElementById('approvedCount').textContent = data.approved || 0;
            document.getElementById('deniedCount').textContent = data.denied || 0;
            
            // Update visitor stats
            document.getElementById('totalVisitors').textContent = data.total_visitors || 0;
            document.getElementById('todayVisitors').textContent = data.today_visitors || 0;
            document.getElementById('activeVisitors').textContent = data.active_visitors || 0;
        })
        .catch(error => console.error('Error fetching stats:', error));
}

// Update stats every 30 seconds
setInterval(updateStatusData, 30000);

// Load initial data
document.addEventListener('DOMContentLoaded', () => {
    loadComplaints();
    updateStatusData();
});

function loadComplaints() {
    const complaintsList = document.getElementById('complaintsList');
    complaintsList.innerHTML = '<p class="loading-message">Loading complaints...</p>';

    fetch('/api/admin/complaints/all')
        .then(response => response.json())
        .then(complaints => {
            if (!complaints || complaints.length === 0) {
                complaintsList.innerHTML = '<p class="empty-message">No complaints found.</p>';
                return;
            }

            let html = '<div class="complaints-grid">';
            complaints.forEach(c => {
                html += `
                    <div class="complaint-card">
                        <div class="complaint-main">
                            <div class="complaint-header">
                                <div class="title-status">
                                    <h3 class="complaint-title">#${c.id} - ${c.title || 'Untitled'}</h3>
                                    <span class="status-badge ${c.status || 'pending'}">${c.status || 'pending'}</span>
                                </div>
                                <div class="complaint-meta">
                                    <span>By ${c.author || 'Anonymous'}</span>
                                    <span>${new Date(c.created_at).toLocaleDateString()}</span>
                                </div>
                            </div>
                            <div class="complaint-content">
                                ${c.full_message || 'No message'}
                            </div>
                        </div>
                        
                        <div class="complaint-details">
                            <div class="detail-item">
                                <span>Type:</span>
                                <span>${c.complaint_type || 'N/A'}</span>
                            </div>
                            <div class="detail-item">
                                <span>Rage Level:</span>
                                <span>${c.rage_level || 'N/A'}</span>
                            </div>
                            <div class="detail-item">
                                <span>Quitting:</span>
                                <span>${c.quitting || 'No'}</span>
                            </div>
                            ${c.zkill_link ? `
                            <div class="detail-item">
                                <span>Zkill:</span>
                                <span><a href="${c.zkill_link}" target="_blank" class="zkill-link">
                                    <img src="https://zkillboard.com/img/wreck.png" alt="zkill" class="zkill-icon" style="height: 16px; width: 16px; margin-right: 4px;">
                                    Link
                                </a></span>
                            </div>
                            ` : ''}
                            
                            <div class="complaint-actions">
                                ${c.status === 'pending' ? `
                                    <button onclick="approveComplaint(${c.id})" class="approve-btn">
                                        <i class="fas fa-check"></i> Approve
                                    </button>
                                    <button onclick="denyComplaint(${c.id})" class="deny-btn">
                                        <i class="fas fa-times"></i> Deny
                                    </button>
                                ` : ''}
                                <button onclick="deleteComplaint(${c.id})" class="delete-btn">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            complaintsList.innerHTML = html;
        })
        .catch(error => {
            console.error('Error:', error);
            complaintsList.innerHTML = '<p class="error-message">Error loading complaints</p>';
        });
}

function displayComplaints(complaints) {
    const container = document.getElementById('complaintsList');
    
    if (!complaints || complaints.length === 0) {
        container.innerHTML = '<p class="empty-message">No complaints found.</p>';
        return;
    }
    
    let html = '<div class="complaints-grid">';
    
    complaints.forEach(complaint => {
        html += `
            <div class="complaint-card" data-id="${complaint.id}">
                <div class="complaint-header">
                    <h3>${complaint.title}</h3>
                    <span class="complaint-status ${complaint.status}">${complaint.status}</span>
                </div>
                <div class="complaint-meta">
                    <span>By ${complaint.author}</span>
                    <span>${complaint.date}</span>
                </div>
                <div class="complaint-preview">
                    ${complaint.preview || complaint.full_message || 'No preview available'}
                </div>
                <div class="complaint-actions">
                    ${complaint.status === 'pending' ? `
                        <button onclick="approveComplaint(${complaint.id})" class="btn approve-btn">
                            <i class="fas fa-check"></i> Approve
                        </button>
                        <button onclick="denyComplaint(${complaint.id})" class="btn deny-btn">
                            <i class="fas fa-times"></i> Deny
                        </button>
                    ` : ''}
                    <button onclick="viewComplaint(${complaint.id})" class="btn view-btn">
                        <i class="fas fa-eye"></i> View
                    </button>
                    <button onclick="deleteComplaint(${complaint.id})" class="btn delete-btn">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

function viewComplaint(id) {
    window.location.href = `/admin/complaint/${id}`;
}

function approveComplaint(id) {
    if (confirm('Are you sure you want to approve this complaint?')) {
        fetch(`/api/admin/complaints/${id}/approve`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                loadComplaints(); // Refresh the complaints list
                const modal = document.querySelector('.modal');
                if (modal) modal.remove();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error approving complaint: ' + error.message);
        });
    }
}

function denyComplaint(id) {
    if (confirm('Are you sure you want to deny this complaint?')) {
        fetch(`/api/admin/complaints/${id}/deny`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                loadComplaints(); // Refresh the complaints list
                const modal = document.querySelector('.modal');
                if (modal) modal.remove();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error denying complaint: ' + error.message);
        });
    }
}

function deleteComplaint(id) {
    if (confirm('Are you sure you want to delete this complaint? This action cannot be undone.')) {
        fetch(`/api/admin/complaints/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert('Complaint deleted successfully!');
                loadComplaints();
                const modal = document.querySelector('.complaint-modal');
                if (modal) modal.remove();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error deleting complaint: ' + error.message);
        });
    }
}

function showPopup(title, message) {
    const popup = document.createElement('div');
    popup.className = 'admin-popup';
    popup.innerHTML = `
        <div class="admin-popup-content">
            <h3>${title}</h3>
            <p>${message}</p>
        </div>
    `;
    document.body.appendChild(popup);
    
    setTimeout(() => {
        popup.remove();
    }, 3000);
}

function getRageLevelClass(rageLevel) {
    if (rageLevel <= 3) return 'rage-low';
    if (rageLevel <= 7) return 'rage-medium';
    if (rageLevel <= 9) return 'rage-high';
    return 'rage-extreme';
}

// Load complaints when the page loads
document.addEventListener('DOMContentLoaded', loadComplaints);

// Add new option
function addOption(targetId) {
    const optionList = document.getElementById(targetId);
    const newOption = document.createElement('div');
    newOption.className = 'option-item';
    newOption.innerHTML = `
        <input type="text" value="" placeholder="Enter new option">
        <button class="delete-option"><i class="fas fa-trash"></i></button>
    `;
    optionList.appendChild(newOption);
    
    // Add event listener to the new delete button
    const deleteBtn = newOption.querySelector('.delete-option');
    deleteBtn.addEventListener('click', () => newOption.remove());
}

// Initialize add option buttons
document.querySelectorAll('.add-option').forEach(button => {
    button.addEventListener('click', () => addOption(button.dataset.target));
});

// Initialize delete option buttons without confirmation
document.querySelectorAll('.delete-option').forEach(button => {
    button.addEventListener('click', function(e) {
        e.preventDefault();
        const optionItem = this.closest('.option-item');
        if (optionItem) {
            optionItem.remove();
        }
    });
});

// Add event listener to the new delete button (for dynamically added options)
function addOption(targetId) {
    const container = document.getElementById(targetId);
    const newOption = document.createElement('div');
    newOption.className = 'option-item';
    newOption.innerHTML = `
        <input type="text" value="" data-value="">
        <button class="delete-option"><i class="fas fa-trash"></i></button>
    `;
    container.appendChild(newOption);

    // Add event listener to the new delete button
    const deleteBtn = newOption.querySelector('.delete-option');
    deleteBtn.addEventListener('click', () => newOption.remove());
}

// Save changes
document.getElementById('saveFormChanges').addEventListener('click', function() {
    const formData = {
        complaintTypes: Array.from(document.querySelectorAll('#complaintTypeOptions .option-item input')).map(input => ({
            text: input.value,
            value: input.dataset.value || input.value.toLowerCase().replace(/\s+/g, '_')
        })),
        blameOptions: Array.from(document.querySelectorAll('#blameOptions .option-item input')).map(input => ({
            text: input.value,
            value: input.dataset.value || input.value.toLowerCase().replace(/\s+/g, '_')
        })),
        rageLevels: Array.from(document.querySelectorAll('#rageLevelOptions .option-item input')).map(input => ({
            text: input.value,
            value: input.dataset.value || input.value.toLowerCase().replace(/\s+/g, '_')
        })),
        quittingOptions: Array.from(document.querySelectorAll('#quittingOptions .option-item input')).map(input => ({
            text: input.value,
            value: input.dataset.value || input.value.toLowerCase().replace(/\s+/g, '_')
        }))
    };

    fetch('/api/admin/form-options', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            alert('Form options updated successfully!');
            // Force reload the options from server
            setTimeout(loadSavedOptions, 500);
        } else {
            alert('Error updating form options: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving changes: ' + error.message);
    });
});

// Reset to defaults
document.getElementById('resetDefaults').addEventListener('click', function() {
    if (confirm('Are you sure you want to reset all options to defaults?')) {
        fetch('/api/admin/form-options/reset', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                location.reload();
            } else {
                alert('Error resetting options: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error resetting to defaults: ' + error.message);
        });
    }
});

// Function to load saved options from the server
function loadSavedOptions() {
    fetch('/api/admin/form-options')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success' && data.data) {
                // Clear existing options
                document.getElementById('complaintTypeOptions').innerHTML = '';
                document.getElementById('blameOptions').innerHTML = '';
                document.getElementById('rageLevelOptions').innerHTML = '';
                document.getElementById('quittingOptions').innerHTML = '';

                // Populate complaint types
                data.data.complaintTypes.forEach(option => {
                    addOptionToList('complaintTypeOptions', option);
                });

                // Populate blame options
                data.data.blameOptions.forEach(option => {
                    addOptionToList('blameOptions', option);
                });

                // Populate rage levels
                data.data.rageLevels.forEach(option => {
                    addOptionToList('rageLevelOptions', option);
                });

                // Populate quitting options
                data.data.quittingOptions.forEach(option => {
                    addOptionToList('quittingOptions', option);
                });
            }
        })
        .catch(error => console.error('Error loading options:', error));
}

// Helper function to add an option to a list
function addOptionToList(listId, option) {
    const list = document.getElementById(listId);
    const div = document.createElement('div');
    div.className = 'option-item';
    div.innerHTML = `
        <input type="text" value="${option.text}" data-value="${option.value}">
        <button class="delete-option"><i class="fas fa-trash"></i></button>
    `;
    list.appendChild(div);
}

// Load saved options when the page loads
document.addEventListener('DOMContentLoaded', loadSavedOptions);

// Form Options Management
let formOptions = {
    complaintTypes: [],
    blameOptions: [],
    rageLevels: [],
    quittingOptions: []
};

// Load form options from server
function loadFormOptions() {
    fetch('/api/admin/form-options')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                formOptions = data.data;
                updateFormOptionsDisplay();
            }
        });
}

// Update the display of form options
function updateFormOptionsDisplay() {
    Object.keys(formOptions).forEach(category => {
        const container = document.getElementById(category);
        if (!container) return;
        
        container.innerHTML = ''; // Clear existing options
        
        formOptions[category].forEach((option, index) => {
            const optionDiv = document.createElement('div');
            optionDiv.className = 'option-item';
            optionDiv.innerHTML = `
                <input type="text" value="${option.text}" data-value="${option.value}">
                <button class="delete-option" data-category="${category}" data-index="${index}">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            container.appendChild(optionDiv);
        });
    });

    // Add delete event listeners
    document.querySelectorAll('.delete-option').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const category = this.dataset.category;
            const index = parseInt(this.dataset.index);
            
            if (confirm('Are you sure you want to delete this option?')) {
                formOptions[category].splice(index, 1);
                updateFormOptionsDisplay();
                saveFormOptions();
            }
        });
    });
}

// Add new option
function addOption(targetId) {
    const newOption = {
        text: '',
        value: ''
    };
    formOptions[targetId].push(newOption);
    updateFormOptionsDisplay();
}

// Save form options to server
function saveFormOptions() {
    // Gather current values from inputs
    Object.keys(formOptions).forEach(category => {
        const container = document.getElementById(category);
        if (!container) return;
        
        formOptions[category] = Array.from(container.querySelectorAll('.option-item')).map(item => {
            const input = item.querySelector('input');
            return {
                text: input.value,
                value: input.value.toLowerCase().replace(/[^a-z0-9]/g, '_')
            };
        });
    });

    // Send to server
    fetch('/api/admin/form-options', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formOptions)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            alert('Form options saved successfully!');
            loadFormOptions(); // Reload to ensure sync
        } else {
            alert('Error saving form options: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving form options');
    });
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    loadFormOptions();
    
    // Add option button listeners
    document.querySelectorAll('.add-option').forEach(button => {
        button.addEventListener('click', () => addOption(button.dataset.target));
    });
    
    // Save button listener
    document.getElementById('saveFormOptions').addEventListener('click', saveFormOptions);
});

// Add this to handle saving all form options
function saveAllOptions() {
    const formOptions = {
        complaint_types: getOptionsFromContainer('complaintTypeOptions'),
        blame_options: getOptionsFromContainer('blameOptions'),
        rage_levels: getOptionsFromContainer('rageLevelOptions'),
        quitting_options: getOptionsFromContainer('quittingOptions')
    };

    fetch('/api/admin/save-form-options', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formOptions)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showPopup('Success', 'All options saved successfully');
        } else {
            showPopup('Error', 'Failed to save options');
        }
    })
    .catch(error => {
        console.error('Error saving options:', error);
        showPopup('Error', 'Failed to save options');
    });
}

function getOptionsFromContainer(containerId) {
    const container = document.getElementById(containerId);
    return Array.from(container.querySelectorAll('.option-input')).map(input => input.value);
}

function addOption(containerId) {
    const container = document.getElementById(containerId);
    const newOption = document.createElement('div');
    newOption.className = 'option-item';
    newOption.innerHTML = `
        <input type="text" value="" class="option-input" placeholder="Enter new option">
        <button type="button" class="delete-option-btn" onclick="deleteOption(this)">Delete</button>
    `;
    container.appendChild(newOption);
}

function deleteOption(button) {
    const optionItem = button.closest('.option-item');
    if (optionItem) {
        optionItem.remove();
    }
}

// Function to add a new option
function addOption(containerId) {
    const container = document.getElementById(containerId);
    const newOption = document.createElement('div');
    newOption.className = 'option-item';
    newOption.innerHTML = `
        <input type="text" value="" class="option-input" placeholder="Enter new option">
        <button type="button" class="delete-option-btn" onclick="deleteOption(this)">Delete</button>
    `;
    container.appendChild(newOption);
}

// Function to delete an option
function deleteOption(button) {
    const optionItem = button.closest('.option-item');
    if (optionItem) {
        optionItem.remove();
    }
}

// Function to save all options
function saveAllOptions() {
    const formOptions = {
        complaint_types: getOptionsFromContainer('complaintTypeOptions'),
        blame_options: getOptionsFromContainer('blameOptions'),
        rage_levels: getOptionsFromContainer('rageLevelOptions'),
        quitting_options: getOptionsFromContainer('quittingOptions')
    };

    fetch('/api/admin/save-form-options', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formOptions)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showPopup('Success', 'All options saved successfully');
        } else {
            showPopup('Error', 'Failed to save options');
        }
    })
    .catch(error => {
        console.error('Error saving options:', error);
        showPopup('Error', 'Failed to save options');
    });
}

// Helper function to get options from a container
function getOptionsFromContainer(containerId) {
    const container = document.getElementById(containerId);
    return Array.from(container.querySelectorAll('.option-input')).map(input => input.value);
}

// Initialize when the document is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers for save button
    document.getElementById('saveFormChanges').addEventListener('click', saveAllOptions);
    
    // Add click handlers for reset button
    document.getElementById('resetDefaults').addEventListener('click', function() {
        if (confirm('Are you sure you want to reset all options to defaults?')) {
            fetch('/api/admin/form-options/reset', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    location.reload();
                } else {
                    showPopup('Error', 'Failed to reset options');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showPopup('Error', 'Failed to reset options');
            });
        }
    });
});
</script>

<style>
.admin-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.admin-dashboard h1 {
    color: #f7931a;
    margin-bottom: 30px;
    text-align: center;
}

.admin-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 2px solid #444;
    padding-bottom: 10px;
}

.tab-button {
    padding: 10px 20px;
    border: none;
    background: #333;
    color: #fff;
    border-radius: 5px 5px 0 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-button:hover {
    background: #444;
}

.tab-button.active {
    background: #f7931a;
    color: #000;
}

.tab-content {
    display: none;
    background: #333;
    border-radius: 8px;
    padding: 20px;
}

.tab-content.active {
    display: block;
}

/* Complaints Tab Styling */
.search-filter-container {
    background: #444;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.search-form {
    display: flex;
    gap: 15px;
    align-items: center;
}

.search-group {
    flex: 1;
    display: flex;
    gap: 10px;
}

.search-group input {
    flex: 1;
    padding: 8px;
    border: none;
    border-radius: 4px;
}

/* Form Stats Styling */
.form-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: #444;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
}

.stat-number {
    font-size: 2em;
    color: #f7931a;
    font-weight: bold;
}

/* Status Tab Styling */
.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.status-card {
    background: #444;
    padding: 20px;
    border-radius: 8px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 15px 0;
}

.status-indicator .dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #2ecc71;
}

.status-indicator.online .dot {
    background: #2ecc71;
}

.processing-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-top: 15px;
}

.stat {
    text-align: center;
}

.stat label {
    display: block;
    margin-bottom: 5px;
    color: #888;
}

.stat span {
    font-size: 1.5em;
    color: #f7931a;
}

/* Toggle Switch */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #f7931a;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.admin-modal {
    display: block;
    position: fixed;
    z-index: 1001; /* Higher than ISK donation modal */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    overflow: auto;
}

.admin-modal-content {
    background-color: #1a1a1a;
    color: #fff;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #444;
    width: 80%;
    max-width: 800px;
    border-radius: 8px;
    position: relative;
}

.admin-close-btn {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 20px;
    top: 10px;
}

.admin-close-btn:hover {
    color: #fff;
}

.complaint-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin: 20px 0;
    padding: 10px;
    background: #2a2a2a;
    border-radius: 4px;
}

.complaint-body {
    background: #2a2a2a;
    padding: 15px;
    border-radius: 4px;
    margin: 20px 0;
}

.admin-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #444;
}

.status-pending {
    color: #ffd700;
}

.status-approved {
    color: #4CAF50;
}

.status-denied {
    color: #f44336;
}

.loading-spinner {
    text-align: center;
    padding: 20px;
    color: #fff;
}

.error {
    color: #f44336;
    text-align: center;
    padding: 20px;
}

/* Button styles */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.approve-btn {
    background-color: #4CAF50;
    color: white;
}

.deny-btn {
    background-color: #f44336;
    color: white;
}

.delete-btn {
    background-color: #ff5722;
    color: white;
}

.btn:hover {
    opacity: 0.8;
    transform: translateY(-2px);
}

.dropdown-settings {
    margin-top: 20px;
    background: #2a2a2a;
    padding: 20px;
    border-radius: 8px;
}

.setting-section {
    margin-bottom: 30px;
}

.setting-section h3 {
    color: #f7931a;
    margin-bottom: 15px;
}

.option-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.option-item {
    display: flex;
    gap: 10px;
    align-items: center;
}

.option-item input {
    flex: 1;
    padding: 8px;
    background: #333;
    border: 1px solid #444;
    border-radius: 4px;
    color: #fff;
}

.delete-option {
    background: #dc3545;
    border: none;
    color: white;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
}

.add-option {
    background: #28a745;
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
}

.form-actions {
    margin-top: 30px;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.btn.primary {
    background: #f7931a;
}

.btn.secondary {
    background: #6c757d;
}

.save-button {
    position: relative;
}

.save-button.saving {
    opacity: 0.7;
    pointer-events: none;
}

.save-button.saving:after {
    content: 'Saving...';
    position: absolute;
    right: -70px;
    top: 50%;
    transform: translateY(-50%);
}

.form-options-container {
    padding: 20px;
}

.option-section {
    margin-bottom: 30px;
}

.option-section h3 {
    margin-bottom: 15px;
}

.options-list {
    margin-bottom: 15px;
}

.option-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 10px;
}

.option-item input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.delete-option {
    background: #ff4444;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px;
    cursor: pointer;
}

.delete-option:hover {
    background: #cc0000;
}

.add-option {
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    margin-top: 10px;
}

.add-option:hover {
    background: #45a049;
}

.form-actions {
    margin-top: 30px;
    display: flex;
    gap: 10px;
}
</style>
{% endblock %}
