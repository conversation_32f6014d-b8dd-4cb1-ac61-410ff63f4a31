from flask import Blueprint, render_template, redirect, url_for, session, request

auth = Blueprint('auth', __name__)

@auth.route('/login')
def login():
    return render_template('login.html', title='Login')

@auth.route('/eve-sso-login')
def eve_sso_login():
    # This would be implemented with EVE Online's SSO system
    # For now, just redirect back to home
    return redirect(url_for('main.index'))