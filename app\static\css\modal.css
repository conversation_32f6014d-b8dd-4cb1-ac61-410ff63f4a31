/* Modal (background) */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.7);
}

/* Modal Content */
.modal-content {
    background-color: #444444;
    color: #f0f0f0;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #555;
    width: 80%;
    max-width: 500px;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
    position: relative;
}

/* Close Button */
.close-btn {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    top: 10px;
    right: 15px;
}

.close-btn:hover,
.close-btn:focus {
    color: #fff;
    text-decoration: none;
}

/* Character name styling */
.character-name {
    font-size: 24px;
    font-weight: bold;
    margin: 20px 0;
    padding: 10px;
    background-color: #333333;
    border: 1px solid #555;
    border-radius: 4px;
    text-align: center;
}

/* Copy button styling */
.copy-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 15px;
    text-align: center;
    text-decoration: none;
    display: block;
    font-size: 16px;
    margin: 10px auto;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.copy-btn:hover {
    background-color: #45a049;
}
