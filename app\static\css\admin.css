:root {
    --dark-bg: #1a1a1a;
    --neon-blue: rgba(0, 243, 255, 0.3);
    --glow-blue: 0 0 10px rgba(0, 243, 255, 0.1);
}

/* Admin page styling */
.admin-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.admin-section {
    margin-bottom: 30px;
    background-color: #444;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.admin-section h2 {
    border-bottom: 2px solid #f7931a;
    padding-bottom: 10px;
    margin-bottom: 20px;
    color: #f0f0f0;
}

/* Complaint list styling */
.complaints-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 20px;
}

.complaint-card {
    display: flex;
    background: var(--dark-bg);
    border: 1px solid var(--neon-blue);
    border-radius: 8px;
    padding: 20px;
    gap: 20px;
    transition: all 0.3s ease;
    box-shadow: var(--glow-blue);
}

.complaint-card:hover {
    background-color: rgba(0, 243, 255, 0.05);
    transform: translateY(-2px);
}

.complaint-main {
    flex: 2;
}

.complaint-header {
    margin-bottom: 10px;
}

.title-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.complaint-title {
    margin: 0;
    color: #fff;
    font-size: 1.1em;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.85em;
}

.status-badge.pending {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border: 1px solid #ffc107;
}

.status-badge.approved {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid #28a745;
}

.status-badge.denied {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border: 1px solid #dc3545;
}

.complaint-content {
    margin: 15px 0;
    line-height: 1.6;
    white-space: pre-wrap;
    word-break: break-word;
}

.complaint-details {
    flex: 1;
    padding: 0 20px;
    border-left: 1px solid var(--neon-blue);
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
}

.complaint-actions {
    margin-top: auto;
    display: flex;
    gap: 10px;
}

.complaint-actions button {
    padding: 6px 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.2s ease;
}

.approve-btn {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid #28a745;
}

.deny-btn {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border: 1px solid #dc3545;
}

.view-btn {
    background: rgba(0, 123, 255, 0.2);
    color: #0088ff;
    border: 1px solid #0088ff;
}

.delete-btn {
    background: rgba(108, 117, 125, 0.2);
    color: #6c757d;
    border: 1px solid #6c757d;
}

.complaint-actions button:hover {
    transform: translateY(-1px);
    filter: brightness(1.2);
}

/* Status indicators */
.complaint-card.pending { border-left: 2px solid #ffd700; }
.complaint-card.approved { border-left: 2px solid #4CAF50; }
.complaint-card.denied { border-left: 2px solid #f44336; }

/* Scrollbar styling */
.complaint-content::-webkit-scrollbar {
    width: 6px;
}

.complaint-content::-webkit-scrollbar-track {
    background: #1a1a1a;
}

.complaint-content::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 3px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .complaints-grid {
        grid-template-columns: 1fr;
    }
}

.complaint-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.complaint-header h3 {
    margin: 0;
    font-size: 1.2em;
    color: #333;
}

.complaint-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
    font-weight: 500;
}

.complaint-status.pending {
    background: #fff3cd;
    color: #856404;
}

.complaint-status.approved {
    background: #d4edda;
    color: #155724;
}

.complaint-status.denied {
    background: #f8d7da;
    color: #721c24;
}

.complaint-meta {
    display: flex;
    gap: 15px;
    font-size: 0.9em;
    color: #666;
    margin-bottom: 15px;
}

.complaint-details {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.detail-row {
    margin-bottom: 8px;
}

.detail-row:last-child {
    margin-bottom: 0;
}

.detail-row strong {
    color: #495057;
}

.complaint-content {
    margin: 15px 0;
    flex: 1; /* Takes up available space */
}

.full-message {
    margin-top: 8px;
    white-space: pre-wrap;
    color: #212529;
    line-height: 1.5;
}

.complaint-actions {
    margin-top: auto; /* Pushes buttons to bottom */
    padding-top: 15px;
    border-top: 1px solid #333;
    display: flex;
    gap: 10px;
}

/* Button styling */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.approve-btn {
    background-color: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.deny-btn {
    background-color: rgba(244, 67, 54, 0.2);
    color: #f44336;
}

.delete-btn {
    background-color: rgba(255, 87, 34, 0.2);
    color: #ff5722;
}

.view-btn {
    background-color: rgba(33, 150, 243, 0.2);
    color: #2196f3;
}

.btn:hover {
    filter: brightness(1.2);
}

.btn i {
    font-size: 11px;
}

/* Message styling */
.loading-message, .empty-message, .error-message {
    text-align: center;
    padding: 20px;
}

.error-message {
    color: #e74c3c;
}

.empty-message {
    color: #aaa;
    font-style: italic;
}

/* Admin login styling */
.admin-login-container {
    max-width: 500px;
    margin: 50px auto;
    padding: 30px;
    background-color: #444;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.admin-login-container h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #f7931a;
}

.admin-login-form .form-group {
    margin-bottom: 20px;
}

.admin-login-form label {
    display: block;
    margin-bottom: 5px;
    color: #f0f0f0;
}

.admin-login-form input {
    width: 100%;
    padding: 10px;
    border: 1px solid #555;
    border-radius: 4px;
    background-color: #333;
    color: #f0f0f0;
}

.login-btn {
    width: 100%;
    padding: 12px;
    background-color: #f7931a;
    color: white;
    font-weight: bold;
}

.login-btn:hover {
    background-color: #e67e22;
}

/* Complaint view styling */
.complaint-view-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #444;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.complaint-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f7931a;
}

.complaint-header h1 {
    margin-top: 0;
    color: #f7931a;
}

.complaint-meta {
    font-size: 0.9rem;
    color: #aaa;
    margin-bottom: 10px;
}

.complaint-status {
    margin-top: 10px;
}

.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-pending {
    background-color: #f39c12;
    color: white;
}

.status-approved {
    background-color: #2ecc71;
    color: white;
}

.status-denied {
    background-color: #e74c3c;
    color: white;
}

.complaint-content {
    margin-bottom: 30px;
    line-height: 1.6;
    white-space: pre-wrap;
}

.back-btn {
    background-color: #7f8c8d;
    color: white;
}

.back-btn:hover {
    background-color: #95a5a6;
}

/* Modal styling */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.7);
}

.modal-content {
    background: #1a1d24;
    border-radius: 8px;
    padding: 0;
    width: 90%;
    max-width: 800px;
    position: relative;
    color: #fff;
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(46, 204, 113, 0.2);
    position: relative;
}

.modal-header .close {
    position: absolute;
    right: 15px;
    top: 15px;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    transition: color 0.2s ease;
}

.modal-header .close:hover {
    color: #2ecc71;
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.complaint-content {
    background: rgba(40, 44, 52, 0.95);
    padding: 15px;
    border-radius: 4px;
    white-space: pre-wrap;
    line-height: 1.5;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid rgba(46, 204, 113, 0.2);
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.complaint-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.complaint-status.pending {
    background: rgba(241, 196, 15, 0.2);
    color: #f1c40f;
}

.complaint-status.approved {
    background: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
}

.complaint-status.denied {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

.zkill-container {
    margin-top: 15px;
    text-align: right;
}

.zkill-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.zkill-link:hover {
    background: rgba(231, 76, 60, 0.3);
    transform: translateY(-1px);
}

.btn {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.approve-btn {
    background: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
}

.approve-btn:hover {
    background: rgba(46, 204, 113, 0.3);
}

.deny-btn {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

.deny-btn:hover {
    background: rgba(231, 76, 60, 0.3);
}

.delete-btn {
    background: rgba(155, 89, 182, 0.2);
    color: #9b59b6;
}

.delete-btn:hover {
    background: rgba(155, 89, 182, 0.3);
}

/* Remove all popup-related styles */
.admin-popup,
.admin-popup-content,
.complaint-view-popup,
.complaint-view-popup-content {
    display: none;
}
